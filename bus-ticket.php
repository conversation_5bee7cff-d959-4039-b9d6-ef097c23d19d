<?php
ob_start();
header('Content-Type: text/html; charset=utf-8');
include('config.php');
include('bus_functions.php');

$booking_reference = $_GET['booking'] ?? '';

if (!$booking_reference) {
    echo "رقم الحجز مطلوب";
    exit;
}

$booking = getBookingDetails($db, $booking_reference);

if (!$booking) {
    echo "الحجز غير موجود";
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تذكرة الباص - <?php echo $booking['booking_reference']; ?></title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        
        .ticket {
            background: white;
            max-width: 600px;
            margin: 0 auto;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .ticket-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .ticket-header h1 {
            margin: 0;
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .ticket-header p {
            margin: 0;
            font-size: 16px;
            opacity: 0.9;
        }
        
        .ticket-body {
            padding: 30px;
        }
        
        .booking-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 25px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .info-label {
            font-weight: bold;
            color: #495057;
        }
        
        .info-value {
            color: #212529;
        }
        
        .trip-details {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 25px;
        }
        
        .route-visual {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
            font-size: 18px;
            font-weight: bold;
        }
        
        .route-arrow {
            margin: 0 20px;
            color: #667eea;
            font-size: 24px;
        }
        
        .payment-info {
            background: #d4edda;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 25px;
        }
        
        .total-amount {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            color: #28a745;
            margin-top: 15px;
        }
        
        .qr-code {
            text-align: center;
            margin: 25px 0;
        }
        
        .booking-reference {
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            color: #667eea;
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .instructions {
            background: #fff3cd;
            padding: 20px;
            border-radius: 8px;
            margin-top: 25px;
        }
        
        .instructions h4 {
            margin-top: 0;
            color: #856404;
        }
        
        .instructions ul {
            margin: 0;
            padding-right: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
            color: #856404;
        }
        
        .print-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            display: block;
            margin: 20px auto;
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .print-btn {
                display: none;
            }
            
            .ticket {
                box-shadow: none;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <div class="ticket">
        <div class="ticket-header">
            <h1>تذكرة الباص</h1>
            <p>نظام حجز مقاعد الباصات</p>
        </div>
        
        <div class="ticket-body">
            <!-- معلومات الحجز -->
            <div class="booking-info">
                <h3 style="margin-top: 0; color: #495057;">معلومات الحجز</h3>
                <div class="info-row">
                    <span class="info-label">رقم الحجز:</span>
                    <span class="info-value"><?php echo $booking['booking_reference']; ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">اسم المسافر:</span>
                    <span class="info-value"><?php echo $booking['passenger_name']; ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">رقم الهاتف:</span>
                    <span class="info-value"><?php echo $booking['passenger_phone']; ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">عدد المقاعد:</span>
                    <span class="info-value"><?php echo $booking['seats_count']; ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">تاريخ الحجز:</span>
                    <span class="info-value"><?php echo date('Y-m-d H:i', strtotime($booking['created_at'])); ?></span>
                </div>
            </div>
            
            <!-- تفاصيل الرحلة -->
            <div class="trip-details">
                <h3 style="margin-top: 0; color: #1976d2;">تفاصيل الرحلة</h3>
                
                <div class="route-visual">
                    <span><?php echo $booking['origin_station']; ?></span>
                    <span class="route-arrow">←</span>
                    <span><?php echo $booking['destination_station']; ?></span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">تاريخ السفر:</span>
                    <span class="info-value"><?php echo date('Y-m-d', strtotime($booking['trip_date'])); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">وقت الانطلاق:</span>
                    <span class="info-value"><?php echo date('g:i A', strtotime($booking['departure_datetime'])); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">وقت الوصول المتوقع:</span>
                    <span class="info-value"><?php echo date('g:i A', strtotime($booking['arrival_datetime'])); ?></span>
                </div>
            </div>
            
            <!-- معلومات الدفع -->
            <div class="payment-info">
                <h3 style="margin-top: 0; color: #155724;">معلومات الدفع</h3>
                <div class="info-row">
                    <span class="info-label">سعر المقعد:</span>
                    <span class="info-value"><?php echo number_format($booking['total_price'] / $booking['seats_count'], 2); ?> ريال</span>
                </div>
                <div class="info-row">
                    <span class="info-label">عدد المقاعد:</span>
                    <span class="info-value"><?php echo $booking['seats_count']; ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">حالة الدفع:</span>
                    <span class="info-value">
                        <?php 
                        $payment_status = [
                            'pending' => 'في انتظار الدفع',
                            'paid' => 'مدفوع',
                            'cancelled' => 'ملغي',
                            'refunded' => 'مسترد'
                        ];
                        echo $payment_status[$booking['payment_status']] ?? $booking['payment_status'];
                        ?>
                    </span>
                </div>
                <div class="total-amount">
                    المبلغ الإجمالي: <?php echo number_format($booking['total_price'], 2); ?> ريال
                </div>
            </div>
            
            <!-- رقم الحجز المميز -->
            <div class="booking-reference">
                رقم الحجز: <?php echo $booking['booking_reference']; ?>
            </div>
            
            <!-- رمز QR (يمكن إضافته لاحقاً) -->
            <div class="qr-code">
                <div style="width: 100px; height: 100px; background: #f0f0f0; margin: 0 auto; display: flex; align-items: center; justify-content: center; border-radius: 8px;">
                    QR Code
                </div>
                <p style="margin: 10px 0 0 0; font-size: 12px; color: #666;">امسح الرمز للتحقق من التذكرة</p>
            </div>
            
            <!-- تعليمات -->
            <div class="instructions">
                <h4>تعليمات مهمة:</h4>
                <ul>
                    <li>يرجى الوصول إلى المحطة قبل موعد الانطلاق بـ 30 دقيقة على الأقل</li>
                    <li>احتفظ بهذه التذكرة وأظهرها عند الصعود للباص</li>
                    <li>تأكد من إحضار هويتك الشخصية</li>
                    <li>في حالة التأخير أو الإلغاء، يرجى الاتصال بخدمة العملاء</li>
                    <li>لا يُسمح بتغيير موعد الرحلة إلا قبل 24 ساعة من الموعد المحدد</li>
                </ul>
            </div>
        </div>
    </div>
    
    <button class="print-btn" onclick="window.print()">
        <i class="fas fa-print"></i> طباعة التذكرة
    </button>
    
    <script>
        // طباعة تلقائية عند فتح الصفحة (اختياري)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
