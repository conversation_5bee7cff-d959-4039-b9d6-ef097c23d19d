-- نظام حجز مقاعد الباصات
-- Bus Booking System Database Tables

-- جدول المدن
CREATE TABLE `bus_cities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT 'اسم المدينة',
  `name_en` varchar(100) DEFAULT NULL COMMENT 'اسم المدينة بالإنجليزية',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'حالة المدينة (1=نشط، 0=غير نشط)',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول المحطات
CREATE TABLE `bus_stations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `city_id` int(11) NOT NULL COMMENT 'معرف المدينة',
  `name` varchar(150) NOT NULL COMMENT 'اسم المحطة',
  `name_en` varchar(150) DEFAULT NULL COMMENT 'اسم المحطة بالإنجليزية',
  `address` text DEFAULT NULL COMMENT 'عنوان المحطة',
  `latitude` decimal(10,8) DEFAULT NULL COMMENT 'خط العرض',
  `longitude` decimal(11,8) DEFAULT NULL COMMENT 'خط الطول',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'حالة المحطة (1=نشط، 0=غير نشط)',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `city_id` (`city_id`),
  FOREIGN KEY (`city_id`) REFERENCES `bus_cities` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الطرق والرحلات
CREATE TABLE `bus_routes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `route_name` varchar(200) NOT NULL COMMENT 'اسم الطريق',
  `route_type` enum('to_mecca','from_mecca') NOT NULL COMMENT 'نوع الرحلة (ذهاب إلى مكة، عودة من مكة)',
  `origin_station_id` int(11) NOT NULL COMMENT 'محطة الانطلاق',
  `destination_station_id` int(11) NOT NULL COMMENT 'محطة الوصول',
  `departure_time` time NOT NULL COMMENT 'وقت الانطلاق',
  `arrival_time` time NOT NULL COMMENT 'وقت الوصول المتوقع',
  `duration_minutes` int(11) NOT NULL COMMENT 'مدة الرحلة بالدقائق',
  `price` decimal(8,2) NOT NULL COMMENT 'سعر المقعد',
  `bus_capacity` int(11) NOT NULL DEFAULT 50 COMMENT 'سعة الباص (عدد المقاعد)',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'حالة الطريق (1=نشط، 0=غير نشط)',
  `daily_schedule` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'رحلة يومية (1=نعم، 0=لا)',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `origin_station_id` (`origin_station_id`),
  KEY `destination_station_id` (`destination_station_id`),
  KEY `route_type` (`route_type`),
  FOREIGN KEY (`origin_station_id`) REFERENCES `bus_stations` (`id`),
  FOREIGN KEY (`destination_station_id`) REFERENCES `bus_stations` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الرحلات اليومية
CREATE TABLE `bus_trips` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `route_id` int(11) NOT NULL COMMENT 'معرف الطريق',
  `trip_date` date NOT NULL COMMENT 'تاريخ الرحلة',
  `departure_datetime` datetime NOT NULL COMMENT 'تاريخ ووقت الانطلاق',
  `arrival_datetime` datetime NOT NULL COMMENT 'تاريخ ووقت الوصول المتوقع',
  `available_seats` int(11) NOT NULL COMMENT 'المقاعد المتاحة',
  `booked_seats` int(11) NOT NULL DEFAULT 0 COMMENT 'المقاعد المحجوزة',
  `status` enum('scheduled','active','completed','cancelled') NOT NULL DEFAULT 'scheduled' COMMENT 'حالة الرحلة',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `route_id` (`route_id`),
  KEY `trip_date` (`trip_date`),
  KEY `status` (`status`),
  UNIQUE KEY `route_date` (`route_id`, `trip_date`),
  FOREIGN KEY (`route_id`) REFERENCES `bus_routes` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الحجوزات
CREATE TABLE `bus_bookings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `booking_reference` varchar(20) NOT NULL COMMENT 'رقم الحجز',
  `trip_id` int(11) NOT NULL COMMENT 'معرف الرحلة',
  `passenger_name` varchar(100) NOT NULL COMMENT 'اسم المسافر',
  `passenger_phone` varchar(20) NOT NULL COMMENT 'رقم هاتف المسافر',
  `passenger_email` varchar(100) DEFAULT NULL COMMENT 'بريد المسافر الإلكتروني',
  `passenger_id_number` varchar(20) DEFAULT NULL COMMENT 'رقم الهوية',
  `seats_count` int(11) NOT NULL COMMENT 'عدد المقاعد',
  `seat_numbers` text DEFAULT NULL COMMENT 'أرقام المقاعد (JSON)',
  `total_price` decimal(10,2) NOT NULL COMMENT 'إجمالي السعر',
  `payment_status` enum('pending','paid','cancelled','refunded') NOT NULL DEFAULT 'pending' COMMENT 'حالة الدفع',
  `payment_method` varchar(50) DEFAULT NULL COMMENT 'طريقة الدفع',
  `booking_status` enum('confirmed','cancelled','completed') NOT NULL DEFAULT 'confirmed' COMMENT 'حالة الحجز',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `booking_reference` (`booking_reference`),
  KEY `trip_id` (`trip_id`),
  KEY `passenger_phone` (`passenger_phone`),
  KEY `booking_status` (`booking_status`),
  KEY `payment_status` (`payment_status`),
  FOREIGN KEY (`trip_id`) REFERENCES `bus_trips` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول المقاعد المحجوزة
CREATE TABLE `bus_seats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `trip_id` int(11) NOT NULL COMMENT 'معرف الرحلة',
  `booking_id` int(11) NOT NULL COMMENT 'معرف الحجز',
  `seat_number` int(11) NOT NULL COMMENT 'رقم المقعد',
  `passenger_name` varchar(100) NOT NULL COMMENT 'اسم المسافر',
  `status` enum('reserved','confirmed','cancelled') NOT NULL DEFAULT 'reserved' COMMENT 'حالة المقعد',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `trip_id` (`trip_id`),
  KEY `booking_id` (`booking_id`),
  UNIQUE KEY `trip_seat` (`trip_id`, `seat_number`),
  FOREIGN KEY (`trip_id`) REFERENCES `bus_trips` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`booking_id`) REFERENCES `bus_bookings` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج البيانات الأساسية

-- إدراج المدن
INSERT INTO `bus_cities` (`name`, `name_en`, `status`) VALUES
('الرياض', 'Riyadh', 1),
('المدينة المنورة', 'Medina', 1),
('مكة المكرمة', 'Mecca', 1);

-- إدراج المحطات
INSERT INTO `bus_stations` (`city_id`, `name`, `name_en`, `address`, `status`) VALUES
-- محطات الرياض
(1, 'محطة البطحاء', 'Al-Batha Station', 'حي البطحاء، الرياض', 1),
-- محطات المدينة المنورة
(2, 'محطة المدينة المنورة المركزية', 'Medina Central Station', 'المدينة المنورة', 1),
-- محطات مكة المكرمة
(3, 'شارع إبراهيم الخليل', 'Ibrahim Al-Khalil Street', 'شارع إبراهيم الخليل، مكة المكرمة', 1),
(3, 'محبس الجن أمام فندق كونكورد', 'Mahbas Al-Jin - Concorde Hotel', 'محبس الجن أمام فندق كونكورد، مكة المكرمة', 1);

-- إدراج الطرق والرحلات
INSERT INTO `bus_routes` (`route_name`, `route_type`, `origin_station_id`, `destination_station_id`, `departure_time`, `arrival_time`, `duration_minutes`, `price`, `bus_capacity`, `status`, `daily_schedule`) VALUES
-- رحلات الذهاب إلى مكة
('الرياض - مكة (محطة البطحاء - شارع إبراهيم الخليل)', 'to_mecca', 1, 3, '13:30:00', '23:30:00', 600, 100.00, 50, 1, 1),
('المدينة المنورة - مكة', 'to_mecca', 2, 3, '13:30:00', '19:30:00', 360, 80.00, 50, 1, 1),
-- رحلات العودة من مكة
('مكة - الرياض (محبس الجن - محطة البطحاء)', 'from_mecca', 4, 1, '13:30:00', '23:30:00', 600, 100.00, 50, 1, 1),
('مكة - المدينة المنورة', 'from_mecca', 4, 2, '13:30:00', '19:30:00', 360, 80.00, 50, 1, 1);
