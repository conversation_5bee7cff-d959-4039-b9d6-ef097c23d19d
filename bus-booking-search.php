<?php
ob_start();
header('Content-Type: text/html; charset=utf-8');
include('config.php');
include('bus_functions.php');

$booking_reference = $_GET['booking'] ?? '';
$booking = null;
$error_message = '';

if ($booking_reference) {
    $booking = getBookingDetails($db, $booking_reference);
    if (!$booking) {
        $error_message = 'الحجز غير موجود أو رقم الحجز غير صحيح';
    }
}

include('header.php');
include('navbar.php');
?>

<style>
.search-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 50px 0;
}

.search-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    padding: 40px;
    margin-bottom: 30px;
}

.booking-details {
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
}

.booking-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

.booking-body {
    padding: 40px;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.btn-search {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: transform 0.2s;
}

.btn-search:hover {
    transform: translateY(-2px);
}

.info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.info-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.info-label {
    font-weight: bold;
    color: #495057;
}

.info-value {
    color: #212529;
}

.status-badge {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-confirmed {
    background: #d4edda;
    color: #155724;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-paid {
    background: #d1ecf1;
    color: #0c5460;
}

.action-buttons {
    margin-top: 30px;
    text-align: center;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-weight: bold;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    margin: 0 10px;
    transition: background 0.3s;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-success {
    background: #28a745;
    color: white;
}

.alert {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.alert-danger {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}
</style>

<div class="search-container">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <!-- نموذج البحث -->
                <div class="search-card">
                    <h2 class="text-center mb-4" style="color: #333;">البحث عن الحجز</h2>
                    <p class="text-center mb-4" style="color: #666;">أدخل رقم الحجز للاطلاع على تفاصيل رحلتك</p>
                    
                    <form method="GET" action="">
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" name="booking" 
                                   placeholder="أدخل رقم الحجز (مثال: BUS20250729001)" 
                                   value="<?php echo htmlspecialchars($booking_reference); ?>" required>
                            <div class="input-group-append">
                                <button class="btn-search" type="submit">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- نتائج البحث -->
                <?php if ($error_message): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo $error_message; ?>
                    </div>
                <?php endif; ?>

                <?php if ($booking): ?>
                    <div class="booking-details">
                        <div class="booking-header">
                            <h3>تفاصيل الحجز</h3>
                            <p>رقم الحجز: <?php echo $booking['booking_reference']; ?></p>
                        </div>
                        
                        <div class="booking-body">
                            <!-- معلومات المسافر -->
                            <div class="section mb-4">
                                <h4 style="color: #333; margin-bottom: 20px;">معلومات المسافر</h4>
                                <div class="info-row">
                                    <span class="info-label">الاسم:</span>
                                    <span class="info-value"><?php echo $booking['passenger_name']; ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">رقم الهاتف:</span>
                                    <span class="info-value"><?php echo $booking['passenger_phone']; ?></span>
                                </div>
                                <?php if ($booking['passenger_email']): ?>
                                <div class="info-row">
                                    <span class="info-label">البريد الإلكتروني:</span>
                                    <span class="info-value"><?php echo $booking['passenger_email']; ?></span>
                                </div>
                                <?php endif; ?>
                                <div class="info-row">
                                    <span class="info-label">عدد المقاعد:</span>
                                    <span class="info-value"><?php echo $booking['seats_count']; ?></span>
                                </div>
                            </div>

                            <!-- تفاصيل الرحلة -->
                            <div class="section mb-4">
                                <h4 style="color: #333; margin-bottom: 20px;">تفاصيل الرحلة</h4>
                                <div class="info-row">
                                    <span class="info-label">من:</span>
                                    <span class="info-value"><?php echo $booking['origin_station']; ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">إلى:</span>
                                    <span class="info-value"><?php echo $booking['destination_station']; ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">تاريخ السفر:</span>
                                    <span class="info-value"><?php echo date('Y-m-d', strtotime($booking['trip_date'])); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">وقت الانطلاق:</span>
                                    <span class="info-value"><?php echo date('g:i A', strtotime($booking['departure_datetime'])); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">وقت الوصول:</span>
                                    <span class="info-value"><?php echo date('g:i A', strtotime($booking['arrival_datetime'])); ?></span>
                                </div>
                            </div>

                            <!-- معلومات الدفع والحالة -->
                            <div class="section mb-4">
                                <h4 style="color: #333; margin-bottom: 20px;">معلومات الدفع</h4>
                                <div class="info-row">
                                    <span class="info-label">المبلغ الإجمالي:</span>
                                    <span class="info-value"><?php echo number_format($booking['total_price'], 2); ?> ريال</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">حالة الدفع:</span>
                                    <span class="info-value">
                                        <?php 
                                        $payment_status = [
                                            'pending' => 'في انتظار الدفع',
                                            'paid' => 'مدفوع',
                                            'cancelled' => 'ملغي',
                                            'refunded' => 'مسترد'
                                        ];
                                        $status_class = [
                                            'pending' => 'status-pending',
                                            'paid' => 'status-paid',
                                            'cancelled' => 'status-cancelled',
                                            'refunded' => 'status-cancelled'
                                        ];
                                        ?>
                                        <span class="status-badge <?php echo $status_class[$booking['payment_status']] ?? ''; ?>">
                                            <?php echo $payment_status[$booking['payment_status']] ?? $booking['payment_status']; ?>
                                        </span>
                                    </span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">حالة الحجز:</span>
                                    <span class="info-value">
                                        <?php 
                                        $booking_status = [
                                            'confirmed' => 'مؤكد',
                                            'cancelled' => 'ملغي',
                                            'completed' => 'مكتمل'
                                        ];
                                        $status_class = [
                                            'confirmed' => 'status-confirmed',
                                            'cancelled' => 'status-cancelled',
                                            'completed' => 'status-confirmed'
                                        ];
                                        ?>
                                        <span class="status-badge <?php echo $status_class[$booking['booking_status']] ?? ''; ?>">
                                            <?php echo $booking_status[$booking['booking_status']] ?? $booking['booking_status']; ?>
                                        </span>
                                    </span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">تاريخ الحجز:</span>
                                    <span class="info-value"><?php echo date('Y-m-d H:i', strtotime($booking['created_at'])); ?></span>
                                </div>
                            </div>

                            <!-- أزرار العمليات -->
                            <div class="action-buttons">
                                <a href="bus-ticket.php?booking=<?php echo $booking['booking_reference']; ?>" 
                                   class="btn btn-primary" target="_blank">
                                    <i class="fas fa-print"></i> طباعة التذكرة
                                </a>
                                
                                <?php if ($booking['booking_status'] == 'confirmed' && strtotime($booking['departure_datetime']) > time() + 24*3600): ?>
                                    <button class="btn btn-danger" onclick="cancelBooking('<?php echo $booking['booking_reference']; ?>')">
                                        <i class="fas fa-times"></i> إلغاء الحجز
                                    </button>
                                <?php endif; ?>
                                
                                <a href="bus-booking.php" class="btn btn-success">
                                    <i class="fas fa-plus"></i> حجز جديد
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
function cancelBooking(bookingReference) {
    if (confirm('هل أنت متأكد من إلغاء هذا الحجز؟\nلا يمكن التراجع عن هذا الإجراء.')) {
        fetch('bus-api.php?action=cancel_booking', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                booking_reference: bookingReference
            })
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                alert('تم إلغاء الحجز بنجاح');
                location.reload();
            } else {
                alert('حدث خطأ: ' + result.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
    }
}
</script>

<?php include('footer.php'); ?>
