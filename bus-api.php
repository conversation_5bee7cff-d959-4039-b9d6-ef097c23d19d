<?php
header('Content-Type: application/json; charset=utf-8');
include('config.php');
include('bus_functions.php');

$action = $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'get_stations':
            $city_id = $_GET['city_id'] ?? 0;
            if ($city_id) {
                $stations = getCityStations($db, $city_id);
                echo json_encode([
                    'success' => true,
                    'stations' => $stations
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'error' => 'معرف المدينة مطلوب'
                ]);
            }
            break;
            
        case 'search_trips':
            $trip_type = $_GET['trip_type'] ?? '';
            $origin_city = $_GET['origin_city'] ?? 0;
            $trip_date = $_GET['trip_date'] ?? '';
            $seats_count = $_GET['seats_count'] ?? 1;
            
            if ($trip_type && $origin_city && $trip_date) {
                $trips = searchTrips($db, $trip_type, $origin_city, $trip_date, $seats_count);
                echo json_encode([
                    'success' => true,
                    'trips' => $trips
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'error' => 'جميع الحقول مطلوبة'
                ]);
            }
            break;
            
        case 'get_trip_details':
            $trip_id = $_GET['trip_id'] ?? 0;
            if ($trip_id) {
                $stmt = $db->prepare("
                    SELECT 
                        t.*,
                        r.route_name,
                        r.price,
                        r.route_type,
                        os.name as origin_station,
                        ds.name as destination_station,
                        oc.name as origin_city,
                        dc.name as destination_city
                    FROM bus_trips t
                    JOIN bus_routes r ON t.route_id = r.id
                    JOIN bus_stations os ON r.origin_station_id = os.id
                    JOIN bus_stations ds ON r.destination_station_id = ds.id
                    JOIN bus_cities oc ON os.city_id = oc.id
                    JOIN bus_cities dc ON ds.city_id = dc.id
                    WHERE t.id = ?
                ");
                $stmt->execute([$trip_id]);
                $trip = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($trip) {
                    echo json_encode([
                        'success' => true,
                        'trip' => $trip
                    ]);
                } else {
                    echo json_encode([
                        'success' => false,
                        'error' => 'الرحلة غير موجودة'
                    ]);
                }
            } else {
                echo json_encode([
                    'success' => false,
                    'error' => 'معرف الرحلة مطلوب'
                ]);
            }
            break;
            
        case 'create_booking':
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $input = json_decode(file_get_contents('php://input'), true);
                
                $trip_id = $input['trip_id'] ?? 0;
                $seats_count = $input['seats_count'] ?? 1;
                $passenger_data = [
                    'name' => $input['passenger_name'] ?? '',
                    'phone' => $input['passenger_phone'] ?? '',
                    'email' => $input['passenger_email'] ?? null,
                    'id_number' => $input['passenger_id_number'] ?? null
                ];
                
                if ($trip_id && $passenger_data['name'] && $passenger_data['phone']) {
                    $result = createBooking($db, $trip_id, $passenger_data, $seats_count);
                    echo json_encode($result);
                } else {
                    echo json_encode([
                        'success' => false,
                        'error' => 'البيانات المطلوبة ناقصة'
                    ]);
                }
            } else {
                echo json_encode([
                    'success' => false,
                    'error' => 'طريقة الطلب غير صحيحة'
                ]);
            }
            break;
            
        case 'get_booking':
            $booking_reference = $_GET['booking_reference'] ?? '';
            if ($booking_reference) {
                $booking = getBookingDetails($db, $booking_reference);
                if ($booking) {
                    echo json_encode([
                        'success' => true,
                        'booking' => $booking
                    ]);
                } else {
                    echo json_encode([
                        'success' => false,
                        'error' => 'الحجز غير موجود'
                    ]);
                }
            } else {
                echo json_encode([
                    'success' => false,
                    'error' => 'رقم الحجز مطلوب'
                ]);
            }
            break;
            
        case 'cancel_booking':
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $input = json_decode(file_get_contents('php://input'), true);
                $booking_reference = $input['booking_reference'] ?? '';
                
                if ($booking_reference) {
                    try {
                        $db->beginTransaction();
                        
                        // الحصول على تفاصيل الحجز
                        $stmt = $db->prepare("SELECT * FROM bus_bookings WHERE booking_reference = ? AND booking_status = 'confirmed'");
                        $stmt->execute([$booking_reference]);
                        $booking = $stmt->fetch(PDO::FETCH_ASSOC);
                        
                        if (!$booking) {
                            throw new Exception('الحجز غير موجود أو تم إلغاؤه مسبقاً');
                        }
                        
                        // إلغاء الحجز
                        $stmt = $db->prepare("UPDATE bus_bookings SET booking_status = 'cancelled', payment_status = 'cancelled' WHERE booking_reference = ?");
                        $stmt->execute([$booking_reference]);
                        
                        // إعادة المقاعد للرحلة
                        $stmt = $db->prepare("
                            UPDATE bus_trips 
                            SET available_seats = available_seats + ?, 
                                booked_seats = booked_seats - ? 
                            WHERE id = ?
                        ");
                        $stmt->execute([$booking['seats_count'], $booking['seats_count'], $booking['trip_id']]);
                        
                        // حذف المقاعد المحجوزة
                        $stmt = $db->prepare("DELETE FROM bus_seats WHERE booking_id = ?");
                        $stmt->execute([$booking['id']]);
                        
                        $db->commit();
                        
                        echo json_encode([
                            'success' => true,
                            'message' => 'تم إلغاء الحجز بنجاح'
                        ]);
                        
                    } catch (Exception $e) {
                        $db->rollBack();
                        echo json_encode([
                            'success' => false,
                            'error' => $e->getMessage()
                        ]);
                    }
                } else {
                    echo json_encode([
                        'success' => false,
                        'error' => 'رقم الحجز مطلوب'
                    ]);
                }
            } else {
                echo json_encode([
                    'success' => false,
                    'error' => 'طريقة الطلب غير صحيحة'
                ]);
            }
            break;
            
        default:
            echo json_encode([
                'success' => false,
                'error' => 'العملية غير مدعومة'
            ]);
            break;
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'حدث خطأ في الخادم: ' . $e->getMessage()
    ]);
}
?>
