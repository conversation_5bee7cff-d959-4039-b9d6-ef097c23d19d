<?php
// ملف تثبيت نظام حجز الباصات المحسن
include('config.php');

echo "<h2>تثبيت نظام حجز الباصات</h2>";
echo "<div style='font-family: Arial; direction: rtl;'>";

$success_count = 0;
$error_count = 0;

try {
    // إنشاء جدول المدن
    echo "<h3>إنشاء جدول المدن...</h3>";
    $db->exec("DROP TABLE IF EXISTS `bus_seats`");
    $db->exec("DROP TABLE IF EXISTS `bus_bookings`");
    $db->exec("DROP TABLE IF EXISTS `bus_trips`");
    $db->exec("DROP TABLE IF EXISTS `bus_routes`");
    $db->exec("DROP TABLE IF EXISTS `bus_stations`");
    $db->exec("DROP TABLE IF EXISTS `bus_cities`");
    
    $sql_cities = "
        CREATE TABLE `bus_cities` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `name` varchar(100) NOT NULL COMMENT 'اسم المدينة',
          `name_en` varchar(100) DEFAULT NULL COMMENT 'اسم المدينة بالإنجليزية',
          `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'حالة المدينة (1=نشط، 0=غير نشط)',
          `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
          `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
          PRIMARY KEY (`id`),
          UNIQUE KEY `name` (`name`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $db->exec($sql_cities);
    echo "<p style='color: green;'>✓ تم إنشاء جدول المدن بنجاح</p>";
    $success_count++;

    // إنشاء جدول المحطات
    echo "<h3>إنشاء جدول المحطات...</h3>";
    $sql_stations = "
        CREATE TABLE `bus_stations` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `city_id` int(11) NOT NULL COMMENT 'معرف المدينة',
          `name` varchar(150) NOT NULL COMMENT 'اسم المحطة',
          `name_en` varchar(150) DEFAULT NULL COMMENT 'اسم المحطة بالإنجليزية',
          `address` text DEFAULT NULL COMMENT 'عنوان المحطة',
          `latitude` decimal(10,8) DEFAULT NULL COMMENT 'خط العرض',
          `longitude` decimal(11,8) DEFAULT NULL COMMENT 'خط الطول',
          `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'حالة المحطة (1=نشط، 0=غير نشط)',
          `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
          `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
          PRIMARY KEY (`id`),
          KEY `city_id` (`city_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $db->exec($sql_stations);
    echo "<p style='color: green;'>✓ تم إنشاء جدول المحطات بنجاح</p>";
    $success_count++;

    // إنشاء جدول الطرق
    echo "<h3>إنشاء جدول الطرق...</h3>";
    $sql_routes = "
        CREATE TABLE `bus_routes` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `route_name` varchar(200) NOT NULL COMMENT 'اسم الطريق',
          `route_type` enum('to_mecca','from_mecca') NOT NULL COMMENT 'نوع الرحلة (ذهاب إلى مكة، عودة من مكة)',
          `origin_station_id` int(11) NOT NULL COMMENT 'محطة الانطلاق',
          `destination_station_id` int(11) NOT NULL COMMENT 'محطة الوصول',
          `departure_time` time NOT NULL COMMENT 'وقت الانطلاق',
          `arrival_time` time NOT NULL COMMENT 'وقت الوصول المتوقع',
          `duration_minutes` int(11) NOT NULL COMMENT 'مدة الرحلة بالدقائق',
          `price` decimal(8,2) NOT NULL COMMENT 'سعر المقعد',
          `bus_capacity` int(11) NOT NULL DEFAULT 50 COMMENT 'سعة الباص (عدد المقاعد)',
          `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'حالة الطريق (1=نشط، 0=غير نشط)',
          `daily_schedule` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'رحلة يومية (1=نعم، 0=لا)',
          `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
          `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
          PRIMARY KEY (`id`),
          KEY `origin_station_id` (`origin_station_id`),
          KEY `destination_station_id` (`destination_station_id`),
          KEY `route_type` (`route_type`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $db->exec($sql_routes);
    echo "<p style='color: green;'>✓ تم إنشاء جدول الطرق بنجاح</p>";
    $success_count++;

    // إنشاء جدول الرحلات
    echo "<h3>إنشاء جدول الرحلات...</h3>";
    $sql_trips = "
        CREATE TABLE `bus_trips` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `route_id` int(11) NOT NULL COMMENT 'معرف الطريق',
          `trip_date` date NOT NULL COMMENT 'تاريخ الرحلة',
          `departure_datetime` datetime NOT NULL COMMENT 'تاريخ ووقت الانطلاق',
          `arrival_datetime` datetime NOT NULL COMMENT 'تاريخ ووقت الوصول المتوقع',
          `available_seats` int(11) NOT NULL COMMENT 'المقاعد المتاحة',
          `booked_seats` int(11) NOT NULL DEFAULT 0 COMMENT 'المقاعد المحجوزة',
          `status` enum('scheduled','active','completed','cancelled') NOT NULL DEFAULT 'scheduled' COMMENT 'حالة الرحلة',
          `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
          `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
          PRIMARY KEY (`id`),
          UNIQUE KEY `route_date` (`route_id`,`trip_date`),
          KEY `trip_date` (`trip_date`),
          KEY `status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $db->exec($sql_trips);
    echo "<p style='color: green;'>✓ تم إنشاء جدول الرحلات بنجاح</p>";
    $success_count++;

    // إنشاء جدول الحجوزات
    echo "<h3>إنشاء جدول الحجوزات...</h3>";
    $sql_bookings = "
        CREATE TABLE `bus_bookings` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `booking_reference` varchar(20) NOT NULL COMMENT 'رقم الحجز المرجعي',
          `trip_id` int(11) NOT NULL COMMENT 'معرف الرحلة',
          `passenger_name` varchar(100) NOT NULL COMMENT 'اسم المسافر',
          `passenger_phone` varchar(20) NOT NULL COMMENT 'رقم هاتف المسافر',
          `passenger_email` varchar(100) DEFAULT NULL COMMENT 'بريد المسافر الإلكتروني',
          `passenger_id_number` varchar(20) DEFAULT NULL COMMENT 'رقم هوية المسافر',
          `seats_count` int(11) NOT NULL DEFAULT 1 COMMENT 'عدد المقاعد المحجوزة',
          `total_price` decimal(10,2) NOT NULL COMMENT 'السعر الإجمالي',
          `booking_status` enum('confirmed','cancelled','completed') NOT NULL DEFAULT 'confirmed' COMMENT 'حالة الحجز',
          `payment_status` enum('pending','paid','cancelled','refunded') NOT NULL DEFAULT 'pending' COMMENT 'حالة الدفع',
          `payment_method` varchar(50) DEFAULT NULL COMMENT 'طريقة الدفع',
          `notes` text DEFAULT NULL COMMENT 'ملاحظات',
          `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
          `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
          PRIMARY KEY (`id`),
          UNIQUE KEY `booking_reference` (`booking_reference`),
          KEY `trip_id` (`trip_id`),
          KEY `passenger_phone` (`passenger_phone`),
          KEY `booking_status` (`booking_status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $db->exec($sql_bookings);
    echo "<p style='color: green;'>✓ تم إنشاء جدول الحجوزات بنجاح</p>";
    $success_count++;

    // إنشاء جدول المقاعد
    echo "<h3>إنشاء جدول المقاعد...</h3>";
    $sql_seats = "
        CREATE TABLE `bus_seats` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `booking_id` int(11) NOT NULL COMMENT 'معرف الحجز',
          `seat_number` varchar(10) NOT NULL COMMENT 'رقم المقعد',
          `passenger_name` varchar(100) DEFAULT NULL COMMENT 'اسم المسافر للمقعد',
          `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
          PRIMARY KEY (`id`),
          KEY `booking_id` (`booking_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $db->exec($sql_seats);
    echo "<p style='color: green;'>✓ تم إنشاء جدول المقاعد بنجاح</p>";
    $success_count++;

    echo "<hr>";
    echo "<h3>ملخص التثبيت:</h3>";
    echo "<p>الجداول المنشأة بنجاح: <strong style='color: green;'>$success_count</strong></p>";
    echo "<p>الأخطاء: <strong style='color: red;'>$error_count</strong></p>";
    
    echo "<p style='color: green; font-size: 18px; font-weight: bold;'>✓ تم تثبيت النظام بنجاح!</p>";
    echo "<p><a href='setup_bus_data.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إعداد البيانات الأساسية</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ عام: " . $e->getMessage() . "</p>";
    $error_count++;
}

echo "</div>";
?>
