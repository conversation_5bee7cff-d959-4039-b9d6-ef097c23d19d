<?php
ob_start();
header('Content-Type: text/html; charset=utf-8');
include('config.php');
include('bus_functions.php');

$trip_id = $_GET['trip_id'] ?? 0;
$seats_count = $_GET['seats'] ?? 1;

if (!$trip_id) {
    header('Location: bus-booking.php');
    exit;
}

// الحصول على تفاصيل الرحلة
$stmt = $db->prepare("
    SELECT 
        t.*,
        r.route_name,
        r.price,
        r.route_type,
        os.name as origin_station,
        ds.name as destination_station,
        oc.name as origin_city,
        dc.name as destination_city
    FROM bus_trips t
    JOIN bus_routes r ON t.route_id = r.id
    JOIN bus_stations os ON r.origin_station_id = os.id
    JOIN bus_stations ds ON r.destination_station_id = ds.id
    JOIN bus_cities oc ON os.city_id = oc.id
    JOIN bus_cities dc ON ds.city_id = dc.id
    WHERE t.id = ?
");
$stmt->execute([$trip_id]);
$trip = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$trip) {
    header('Location: bus-booking.php');
    exit;
}

$total_price = $trip['price'] * $seats_count;

include('header.php');
include('navbar.php');
?>

<style>
.booking-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 50px 0;
}

.booking-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
}

.trip-summary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
}

.booking-form {
    padding: 40px;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #333;
    font-size: 16px;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.btn-confirm {
    background: #28a745;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 8px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    width: 100%;
    transition: background 0.3s;
}

.btn-confirm:hover {
    background: #218838;
}

.btn-back {
    background: #6c757d;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    text-decoration: none;
    display: inline-block;
    margin-bottom: 20px;
}

.trip-detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.price-summary {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-top: 20px;
}

.total-price {
    font-size: 24px;
    font-weight: bold;
    color: #667eea;
    text-align: center;
    margin-top: 15px;
}
</style>

<div class="booking-container">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <a href="bus-booking.php" class="btn-back">
                    <i class="fas fa-arrow-right"></i> العودة للبحث
                </a>
                
                <div class="booking-card">
                    <!-- ملخص الرحلة -->
                    <div class="trip-summary">
                        <h3 class="mb-3">تفاصيل الرحلة</h3>
                        <div class="trip-detail-row">
                            <span>من:</span>
                            <span><?php echo $trip['origin_station']; ?></span>
                        </div>
                        <div class="trip-detail-row">
                            <span>إلى:</span>
                            <span><?php echo $trip['destination_station']; ?></span>
                        </div>
                        <div class="trip-detail-row">
                            <span>تاريخ السفر:</span>
                            <span><?php echo date('Y-m-d', strtotime($trip['trip_date'])); ?></span>
                        </div>
                        <div class="trip-detail-row">
                            <span>وقت الانطلاق:</span>
                            <span><?php echo date('g:i A', strtotime($trip['departure_datetime'])); ?></span>
                        </div>
                        <div class="trip-detail-row">
                            <span>وقت الوصول:</span>
                            <span><?php echo date('g:i A', strtotime($trip['arrival_datetime'])); ?></span>
                        </div>
                        <div class="trip-detail-row">
                            <span>عدد المقاعد:</span>
                            <span><?php echo $seats_count; ?></span>
                        </div>
                        <div class="trip-detail-row">
                            <span>سعر المقعد:</span>
                            <span><?php echo $trip['price']; ?> ريال</span>
                        </div>
                        <hr style="border-color: rgba(255,255,255,0.3);">
                        <div class="trip-detail-row" style="font-size: 18px; font-weight: bold;">
                            <span>المجموع:</span>
                            <span><?php echo $total_price; ?> ريال</span>
                        </div>
                    </div>
                    
                    <!-- نموذج الحجز -->
                    <div class="booking-form">
                        <h3 class="mb-4">بيانات المسافر</h3>
                        
                        <form id="bookingForm">
                            <input type="hidden" name="trip_id" value="<?php echo $trip_id; ?>">
                            <input type="hidden" name="seats_count" value="<?php echo $seats_count; ?>">
                            
                            <div class="form-group">
                                <label for="passenger_name">الاسم الكامل *</label>
                                <input type="text" class="form-control" id="passenger_name" name="passenger_name" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="passenger_phone">رقم الهاتف *</label>
                                <input type="tel" class="form-control" id="passenger_phone" name="passenger_phone" 
                                       pattern="[0-9]{10}" placeholder="05xxxxxxxx" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="passenger_email">البريد الإلكتروني (اختياري)</label>
                                <input type="email" class="form-control" id="passenger_email" name="passenger_email">
                            </div>
                            
                            <div class="form-group">
                                <label for="passenger_id_number">رقم الهوية (اختياري)</label>
                                <input type="text" class="form-control" id="passenger_id_number" name="passenger_id_number">
                            </div>
                            
                            <div class="price-summary">
                                <h4>ملخص السعر</h4>
                                <div class="trip-detail-row">
                                    <span><?php echo $seats_count; ?> مقعد × <?php echo $trip['price']; ?> ريال</span>
                                    <span><?php echo $total_price; ?> ريال</span>
                                </div>
                                <div class="total-price">
                                    المجموع: <?php echo $total_price; ?> ريال
                                </div>
                            </div>
                            
                            <button type="submit" class="btn-confirm" id="confirmBtn">
                                <i class="fas fa-check"></i>
                                تأكيد الحجز
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal للنتيجة -->
<div class="modal fade" id="resultModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">نتيجة الحجز</h5>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- سيتم ملء المحتوى بواسطة JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" id="printBtn" style="display: none;">طباعة التذكرة</button>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('bookingForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const confirmBtn = document.getElementById('confirmBtn');
    
    // تعطيل الزر وإظهار مؤشر التحميل
    confirmBtn.disabled = true;
    confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحجز...';
    
    // تحويل FormData إلى JSON
    const data = {};
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }
    
    // إرسال طلب الحجز
    fetch('bus-api.php?action=create_booking', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            // نجح الحجز
            document.getElementById('modalTitle').textContent = 'تم الحجز بنجاح!';
            document.getElementById('modalBody').innerHTML = `
                <div class="alert alert-success">
                    <h4><i class="fas fa-check-circle"></i> تم تأكيد حجزك</h4>
                    <p><strong>رقم الحجز:</strong> ${result.booking_reference}</p>
                    <p><strong>المبلغ الإجمالي:</strong> ${result.total_price} ريال</p>
                    <p>يرجى الاحتفاظ برقم الحجز للمراجعة</p>
                </div>
            `;
            document.getElementById('printBtn').style.display = 'inline-block';
            document.getElementById('printBtn').onclick = function() {
                window.open('bus-ticket.php?booking=' + result.booking_reference, '_blank');
            };
        } else {
            // فشل الحجز
            document.getElementById('modalTitle').textContent = 'فشل في الحجز';
            document.getElementById('modalBody').innerHTML = `
                <div class="alert alert-danger">
                    <h4><i class="fas fa-exclamation-triangle"></i> حدث خطأ</h4>
                    <p>${result.error}</p>
                </div>
            `;
        }
        
        // إظهار النافذة المنبثقة
        $('#resultModal').modal('show');
        
        // إعادة تفعيل الزر
        confirmBtn.disabled = false;
        confirmBtn.innerHTML = '<i class="fas fa-check"></i> تأكيد الحجز';
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('modalTitle').textContent = 'خطأ في الاتصال';
        document.getElementById('modalBody').innerHTML = `
            <div class="alert alert-danger">
                <h4><i class="fas fa-exclamation-triangle"></i> خطأ في الاتصال</h4>
                <p>حدث خطأ في الاتصال بالخادم. يرجى المحاولة مرة أخرى.</p>
            </div>
        `;
        $('#resultModal').modal('show');
        
        // إعادة تفعيل الزر
        confirmBtn.disabled = false;
        confirmBtn.innerHTML = '<i class="fas fa-check"></i> تأكيد الحجز';
    });
});
</script>

<?php include('footer.php'); ?>
