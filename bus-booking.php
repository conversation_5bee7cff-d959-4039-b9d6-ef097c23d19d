<?php
ob_start();
header('Content-Type: text/html; charset=utf-8');
include('config.php');
include('bus_functions.php');

// الحصول على جميع المدن
$cities = getAllCities($db);

include('header.php');
include('navbar.php');
?>

<style>
.bus-booking-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 50px 0;
}

.booking-form-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    padding: 40px;
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #333;
    font-size: 16px;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.btn-search {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 8px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    width: 100%;
    transition: transform 0.2s;
}

.btn-search:hover {
    transform: translateY(-2px);
}

.trip-type-selector {
    display: flex;
    gap: 20px;
    margin-bottom: 25px;
}

.trip-type-option {
    flex: 1;
    position: relative;
}

.trip-type-option input[type="radio"] {
    display: none;
}

.trip-type-option label {
    display: block;
    padding: 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    background: #f8f9fa;
}

.trip-type-option input[type="radio"]:checked + label {
    border-color: #667eea;
    background: #667eea;
    color: white;
}

.results-container {
    margin-top: 30px;
}

.trip-card {
    background: white;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border: 1px solid #e1e5e9;
    transition: transform 0.2s;
}

.trip-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.trip-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 15px;
}

.trip-route {
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

.trip-price {
    font-size: 24px;
    font-weight: bold;
    color: #667eea;
}

.trip-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.trip-detail {
    text-align: center;
}

.trip-detail-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.trip-detail-value {
    font-size: 16px;
    font-weight: bold;
    color: #333;
}

.btn-book {
    background: #28a745;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 6px;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.3s;
}

.btn-book:hover {
    background: #218838;
}

.no-trips {
    text-align: center;
    padding: 40px;
    color: #666;
    font-size: 18px;
}
</style>

<div class="bus-booking-container">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="booking-form-card">
                    <h2 class="text-center mb-4" style="color: #333;">حجز مقاعد الباصات</h2>
                    <p class="text-center mb-4" style="color: #666;">احجز رحلتك إلى مكة المكرمة أو العودة منها بسهولة</p>
                    
                    <form id="busBookingForm">
                        <!-- اختيار نوع الرحلة -->
                        <div class="form-group">
                            <label>نوع الرحلة</label>
                            <div class="trip-type-selector">
                                <div class="trip-type-option">
                                    <input type="radio" id="to_mecca" name="trip_type" value="to_mecca" checked>
                                    <label for="to_mecca">
                                        <i class="fas fa-arrow-left"></i>
                                        ذهاب إلى مكة
                                    </label>
                                </div>
                                <div class="trip-type-option">
                                    <input type="radio" id="from_mecca" name="trip_type" value="from_mecca">
                                    <label for="from_mecca">
                                        <i class="fas fa-arrow-right"></i>
                                        عودة من مكة
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- اختيار المدينة -->
                        <div class="form-group">
                            <label for="origin_city">المدينة</label>
                            <select class="form-control" id="origin_city" name="origin_city" required>
                                <option value="">اختر المدينة</option>
                                <?php foreach ($cities as $city): ?>
                                    <?php if ($city['name'] != 'مكة المكرمة'): ?>
                                        <option value="<?php echo $city['id']; ?>"><?php echo $city['name']; ?></option>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- اختيار المحطة -->
                        <div class="form-group">
                            <label for="origin_station">المحطة</label>
                            <select class="form-control" id="origin_station" name="origin_station" required disabled>
                                <option value="">اختر المحطة</option>
                            </select>
                        </div>

                        <!-- تاريخ السفر -->
                        <div class="form-group">
                            <label for="trip_date">تاريخ السفر</label>
                            <input type="date" class="form-control" id="trip_date" name="trip_date" 
                                   min="<?php echo date('Y-m-d'); ?>" 
                                   max="<?php echo date('Y-m-d', strtotime('+30 days')); ?>" required>
                        </div>

                        <!-- عدد المقاعد -->
                        <div class="form-group">
                            <label for="seats_count">عدد المقاعد</label>
                            <select class="form-control" id="seats_count" name="seats_count" required>
                                <option value="1">1 مقعد</option>
                                <option value="2">2 مقعد</option>
                                <option value="3">3 مقاعد</option>
                                <option value="4">4 مقاعد</option>
                                <option value="5">5 مقاعد</option>
                            </select>
                        </div>

                        <!-- زر البحث -->
                        <button type="submit" class="btn-search">
                            <i class="fas fa-search"></i>
                            عرض الرحلات المتاحة
                        </button>
                    </form>
                </div>

                <!-- نتائج البحث -->
                <div id="searchResults" class="results-container" style="display: none;">
                    <h3 style="color: white; margin-bottom: 20px;">الرحلات المتاحة</h3>
                    <div id="tripsContainer"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحديث المحطات عند تغيير المدينة
document.getElementById('origin_city').addEventListener('change', function() {
    const cityId = this.value;
    const stationSelect = document.getElementById('origin_station');
    
    // إعادة تعيين المحطات
    stationSelect.innerHTML = '<option value="">اختر المحطة</option>';
    stationSelect.disabled = !cityId;
    
    if (cityId) {
        // جلب المحطات من الخادم
        fetch('bus-api.php?action=get_stations&city_id=' + cityId)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    data.stations.forEach(station => {
                        const option = document.createElement('option');
                        option.value = station.id;
                        option.textContent = station.name;
                        stationSelect.appendChild(option);
                    });
                }
            })
            .catch(error => console.error('Error:', error));
    }
});

// معالجة نموذج البحث
document.getElementById('busBookingForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const searchParams = new URLSearchParams();
    
    for (let [key, value] of formData.entries()) {
        searchParams.append(key, value);
    }
    
    // إظهار مؤشر التحميل
    const resultsContainer = document.getElementById('searchResults');
    const tripsContainer = document.getElementById('tripsContainer');
    
    resultsContainer.style.display = 'block';
    tripsContainer.innerHTML = '<div class="text-center" style="color: white; padding: 40px;"><i class="fas fa-spinner fa-spin fa-2x"></i><br>جاري البحث...</div>';
    
    // البحث عن الرحلات
    fetch('bus-api.php?action=search_trips&' + searchParams.toString())
        .then(response => response.json())
        .then(data => {
            if (data.success && data.trips.length > 0) {
                displayTrips(data.trips);
            } else {
                tripsContainer.innerHTML = '<div class="no-trips">لا توجد رحلات متاحة في التاريخ المحدد</div>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            tripsContainer.innerHTML = '<div class="no-trips" style="color: red;">حدث خطأ في البحث</div>';
        });
});

// عرض الرحلات
function displayTrips(trips) {
    const tripsContainer = document.getElementById('tripsContainer');
    let html = '';
    
    trips.forEach(trip => {
        const departureTime = new Date(trip.departure_datetime).toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });
        const arrivalTime = new Date(trip.arrival_datetime).toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });
        
        html += `
            <div class="trip-card">
                <div class="trip-header">
                    <div class="trip-route">${trip.origin_station} ← ${trip.destination_station}</div>
                    <div class="trip-price">${trip.price} ريال</div>
                </div>
                <div class="trip-details">
                    <div class="trip-detail">
                        <div class="trip-detail-label">وقت الانطلاق</div>
                        <div class="trip-detail-value">${departureTime}</div>
                    </div>
                    <div class="trip-detail">
                        <div class="trip-detail-label">وقت الوصول</div>
                        <div class="trip-detail-value">${arrivalTime}</div>
                    </div>
                    <div class="trip-detail">
                        <div class="trip-detail-label">المقاعد المتاحة</div>
                        <div class="trip-detail-value">${trip.available_seats}</div>
                    </div>
                </div>
                <button class="btn-book" onclick="bookTrip(${trip.trip_id})">
                    <i class="fas fa-ticket-alt"></i>
                    احجز الآن
                </button>
            </div>
        `;
    });
    
    tripsContainer.innerHTML = html;
}

// حجز الرحلة
function bookTrip(tripId) {
    const seatsCount = document.getElementById('seats_count').value;
    window.location.href = `bus-booking-form.php?trip_id=${tripId}&seats=${seatsCount}`;
}

// تعيين التاريخ الافتراضي لليوم الحالي
document.getElementById('trip_date').value = new Date().toISOString().split('T')[0];
</script>

<?php include('footer.php'); ?>
