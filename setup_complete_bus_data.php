<?php
// ملف إعداد البيانات الكاملة لنظام الباصات
include('config.php');

echo "<h2>إعداد البيانات الكاملة لنظام الباصات</h2>";
echo "<div style='font-family: Arial; direction: rtl;'>";

try {
    // حذف البيانات الموجودة
    echo "<h3>حذف البيانات الموجودة...</h3>";
    $db->exec("DELETE FROM bus_seats");
    $db->exec("DELETE FROM bus_bookings");
    $db->exec("DELETE FROM bus_trips");
    $db->exec("DELETE FROM bus_routes");
    $db->exec("DELETE FROM bus_stations");
    $db->exec("DELETE FROM bus_cities");
    echo "<p style='color: green;'>✓ تم حذف البيانات الموجودة</p>";

    // إضافة المدن
    echo "<h3>إضافة المدن...</h3>";
    $cities_data = [
        ['name' => 'الرياض', 'name_en' => 'Riyadh'],
        ['name' => 'المدينة المنورة', 'name_en' => 'Medina'],
        ['name' => 'مكة المكرمة', 'name_en' => 'Mecca']
    ];
    
    $city_ids = [];
    foreach ($cities_data as $city) {
        $stmt = $db->prepare("INSERT INTO bus_cities (name, name_en, status) VALUES (?, ?, 1)");
        $stmt->execute([$city['name'], $city['name_en']]);
        $city_ids[$city['name']] = $db->lastInsertId();
        echo "<p style='color: green;'>✓ تم إضافة مدينة: {$city['name']}</p>";
    }

    // إضافة المحطات
    echo "<h3>إضافة المحطات...</h3>";
    $stations_data = [
        // محطات الرياض
        ['city' => 'الرياض', 'name' => 'محطة الرياض المركزية', 'name_en' => 'Riyadh Central Station'],
        ['city' => 'الرياض', 'name' => 'محطة الملك فهد', 'name_en' => 'King Fahd Station'],
        
        // محطات المدينة المنورة
        ['city' => 'المدينة المنورة', 'name' => 'محطة المدينة المركزية', 'name_en' => 'Medina Central Station'],
        ['city' => 'المدينة المنورة', 'name' => 'محطة الحرم النبوي', 'name_en' => 'Prophet\'s Mosque Station'],
        
        // محطات مكة المكرمة
        ['city' => 'مكة المكرمة', 'name' => 'محطة مكة المركزية', 'name_en' => 'Mecca Central Station'],
        ['city' => 'مكة المكرمة', 'name' => 'محطة الحرم المكي', 'name_en' => 'Grand Mosque Station']
    ];
    
    $station_ids = [];
    foreach ($stations_data as $station) {
        $stmt = $db->prepare("INSERT INTO bus_stations (city_id, name, name_en, status) VALUES (?, ?, ?, 1)");
        $stmt->execute([$city_ids[$station['city']], $station['name'], $station['name_en']]);
        $station_ids[$station['name']] = $db->lastInsertId();
        echo "<p style='color: green;'>✓ تم إضافة محطة: {$station['name']}</p>";
    }

    // إضافة الطرق
    echo "<h3>إضافة الطرق...</h3>";
    $routes_data = [
        // رحلات ذهاب إلى مكة
        [
            'route_name' => 'الرياض - مكة المكرمة',
            'route_type' => 'to_mecca',
            'origin_station' => 'محطة الرياض المركزية',
            'destination_station' => 'محطة مكة المركزية',
            'departure_time' => '13:30:00',
            'arrival_time' => '21:30:00',
            'duration_minutes' => 480,
            'price' => 100.00,
            'bus_capacity' => 50
        ],
        [
            'route_name' => 'المدينة المنورة - مكة المكرمة',
            'route_type' => 'to_mecca',
            'origin_station' => 'محطة المدينة المركزية',
            'destination_station' => 'محطة مكة المركزية',
            'departure_time' => '13:30:00',
            'arrival_time' => '17:30:00',
            'duration_minutes' => 240,
            'price' => 100.00,
            'bus_capacity' => 50
        ],
        
        // رحلات عودة من مكة
        [
            'route_name' => 'مكة المكرمة - الرياض',
            'route_type' => 'from_mecca',
            'origin_station' => 'محطة مكة المركزية',
            'destination_station' => 'محطة الرياض المركزية',
            'departure_time' => '13:30:00',
            'arrival_time' => '21:30:00',
            'duration_minutes' => 480,
            'price' => 100.00,
            'bus_capacity' => 50
        ],
        [
            'route_name' => 'مكة المكرمة - المدينة المنورة',
            'route_type' => 'from_mecca',
            'origin_station' => 'محطة مكة المركزية',
            'destination_station' => 'محطة المدينة المركزية',
            'departure_time' => '13:30:00',
            'arrival_time' => '17:30:00',
            'duration_minutes' => 240,
            'price' => 100.00,
            'bus_capacity' => 50
        ]
    ];
    
    $route_ids = [];
    foreach ($routes_data as $route) {
        $stmt = $db->prepare("
            INSERT INTO bus_routes (
                route_name, route_type, origin_station_id, destination_station_id,
                departure_time, arrival_time, duration_minutes, price, bus_capacity,
                status, daily_schedule
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1, 1)
        ");
        
        $stmt->execute([
            $route['route_name'],
            $route['route_type'],
            $station_ids[$route['origin_station']],
            $station_ids[$route['destination_station']],
            $route['departure_time'],
            $route['arrival_time'],
            $route['duration_minutes'],
            $route['price'],
            $route['bus_capacity']
        ]);
        
        $route_ids[] = $db->lastInsertId();
        echo "<p style='color: green;'>✓ تم إضافة طريق: {$route['route_name']}</p>";
    }

    // إنشاء الرحلات للأيام القادمة
    echo "<h3>إنشاء الرحلات للأيام القادمة...</h3>";
    
    $trips_created = 0;
    
    // إنشاء رحلات لمدة 30 يوم قادم
    for ($i = 0; $i < 30; $i++) {
        $trip_date = date('Y-m-d', strtotime("+$i days"));
        
        foreach ($routes_data as $index => $route) {
            $route_id = $route_ids[$index];
            
            // إنشاء تاريخ ووقت الانطلاق والوصول
            $departure_datetime = $trip_date . ' ' . $route['departure_time'];
            $arrival_datetime = $trip_date . ' ' . $route['arrival_time'];
            
            try {
                $stmt = $db->prepare("
                    INSERT INTO bus_trips (
                        route_id, trip_date, departure_datetime, arrival_datetime, 
                        available_seats, booked_seats, status
                    ) VALUES (?, ?, ?, ?, ?, 0, 'scheduled')
                ");
                
                $stmt->execute([
                    $route_id,
                    $trip_date,
                    $departure_datetime,
                    $arrival_datetime,
                    $route['bus_capacity']
                ]);
                
                $trips_created++;
                
            } catch (PDOException $e) {
                echo "<p style='color: red;'>خطأ في إنشاء رحلة {$route['route_name']} ليوم $trip_date: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    echo "<p style='color: green;'>✓ تم إنشاء $trips_created رحلة بنجاح</p>";

    // عرض إحصائيات النظام
    echo "<hr><h3>إحصائيات النظام:</h3>";
    
    $stats = [
        'المدن' => $db->query("SELECT COUNT(*) FROM bus_cities")->fetchColumn(),
        'المحطات' => $db->query("SELECT COUNT(*) FROM bus_stations")->fetchColumn(),
        'الطرق' => $db->query("SELECT COUNT(*) FROM bus_routes")->fetchColumn(),
        'الرحلات' => $db->query("SELECT COUNT(*) FROM bus_trips")->fetchColumn()
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 50%;'>";
    foreach ($stats as $item => $count) {
        echo "<tr><td><strong>$item</strong></td><td>$count</td></tr>";
    }
    echo "</table>";
    
    // عرض رحلات اليوم
    echo "<hr><h3>رحلات اليوم (" . date('Y-m-d') . "):</h3>";
    $stmt = $db->prepare("
        SELECT 
            t.departure_datetime,
            t.available_seats,
            r.route_name,
            r.price,
            os.name as origin_station,
            ds.name as destination_station
        FROM bus_trips t
        JOIN bus_routes r ON t.route_id = r.id
        JOIN bus_stations os ON r.origin_station_id = os.id
        JOIN bus_stations ds ON r.destination_station_id = ds.id
        WHERE t.trip_date = CURDATE()
        AND t.status = 'scheduled'
        ORDER BY t.departure_datetime
    ");
    $stmt->execute();
    $today_trips = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($today_trips) > 0) {
        echo "<table border='1' style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>من</th><th>إلى</th><th>وقت الانطلاق</th><th>المقاعد المتاحة</th><th>السعر</th>";
        echo "</tr>";
        
        foreach ($today_trips as $trip) {
            echo "<tr>";
            echo "<td>{$trip['origin_station']}</td>";
            echo "<td>{$trip['destination_station']}</td>";
            echo "<td>" . date('g:i A', strtotime($trip['departure_datetime'])) . "</td>";
            echo "<td>{$trip['available_seats']}</td>";
            echo "<td>{$trip['price']} ريال</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>لا توجد رحلات مجدولة لليوم</p>";
    }
    
    echo "<hr>";
    echo "<p style='color: green; font-size: 18px;'><strong>✓ تم إعداد النظام بنجاح!</strong></p>";
    echo "<p><a href='bus-booking.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>انتقل إلى صفحة الحجز</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
}

echo "</div>";
?>
