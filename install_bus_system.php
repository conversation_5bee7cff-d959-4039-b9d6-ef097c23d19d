<?php
// ملف تثبيت نظام حجز الباصات
include('config.php');

echo "<h2>تثبيت نظام حجز الباصات</h2>";
echo "<div style='font-family: Arial; direction: rtl;'>";

$success_count = 0;
$error_count = 0;

try {
    // إنشاء الجداول مباشرة
    $tables = [
        'bus_cities' => "
            CREATE TABLE `bus_cities` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `name` varchar(100) NOT NULL COMMENT 'اسم المدينة',
              `name_en` varchar(100) DEFAULT NULL COMMENT 'اسم المدينة بالإنجليزية',
              `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'حالة المدينة (1=نشط، 0=غير نشط)',
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
              PRIMARY KEY (`id`),
              UNIQUE KEY `name` (`name`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",

        'bus_stations' => "
            CREATE TABLE `bus_stations` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `city_id` int(11) NOT NULL COMMENT 'معرف المدينة',
              `name` varchar(150) NOT NULL COMMENT 'اسم المحطة',
              `name_en` varchar(150) DEFAULT NULL COMMENT 'اسم المحطة بالإنجليزية',
              `address` text DEFAULT NULL COMMENT 'عنوان المحطة',
              `latitude` decimal(10,8) DEFAULT NULL COMMENT 'خط العرض',
              `longitude` decimal(11,8) DEFAULT NULL COMMENT 'خط الطول',
              `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'حالة المحطة (1=نشط، 0=غير نشط)',
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
              PRIMARY KEY (`id`),
              KEY `city_id` (`city_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",

        'bus_routes' => "
            CREATE TABLE `bus_routes` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `route_name` varchar(200) NOT NULL COMMENT 'اسم الطريق',
              `route_type` enum('to_mecca','from_mecca') NOT NULL COMMENT 'نوع الرحلة (ذهاب إلى مكة، عودة من مكة)',
              `origin_station_id` int(11) NOT NULL COMMENT 'محطة الانطلاق',
              `destination_station_id` int(11) NOT NULL COMMENT 'محطة الوصول',
              `departure_time` time NOT NULL COMMENT 'وقت الانطلاق',
              `arrival_time` time NOT NULL COMMENT 'وقت الوصول المتوقع',
              `duration_minutes` int(11) NOT NULL COMMENT 'مدة الرحلة بالدقائق',
              `price` decimal(8,2) NOT NULL COMMENT 'سعر المقعد',
              `bus_capacity` int(11) NOT NULL DEFAULT 50 COMMENT 'سعة الباص (عدد المقاعد)',
              `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'حالة الطريق (1=نشط، 0=غير نشط)',
              `daily_schedule` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'رحلة يومية (1=نعم، 0=لا)',
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
              PRIMARY KEY (`id`),
              KEY `origin_station_id` (`origin_station_id`),
              KEY `destination_station_id` (`destination_station_id`),
              KEY `route_type` (`route_type`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
    ];

    foreach ($tables as $table_name => $sql) {
        try {
            // حذف الجدول إذا كان موجوداً
            $db->exec("DROP TABLE IF EXISTS `$table_name`");

            // إنشاء الجدول
            $db->exec($sql);
            $success_count++;
            echo "<p style='color: green;'>✓ تم إنشاء جدول $table_name بنجاح</p>";
        } catch (PDOException $e) {
            $error_count++;
            echo "<p style='color: red;'>✗ خطأ في إنشاء جدول $table_name: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<hr>";
    echo "<h3>ملخص التثبيت:</h3>";
    echo "<p><strong>الاستعلامات الناجحة:</strong> $success_count</p>";
    echo "<p><strong>الاستعلامات الفاشلة:</strong> $error_count</p>";
    
    if ($error_count == 0) {
        echo "<p style='color: green; font-size: 18px;'><strong>✓ تم تثبيت نظام حجز الباصات بنجاح!</strong></p>";
        echo "<p><a href='bus-booking.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>انتقل إلى صفحة الحجز</a></p>";
    } else {
        echo "<p style='color: red; font-size: 18px;'><strong>✗ حدثت أخطاء أثناء التثبيت</strong></p>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ عام: " . $e->getMessage() . "</p>";
}
?>
