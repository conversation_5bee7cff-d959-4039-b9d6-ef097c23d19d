<?php
// ملف تثبيت نظام حجز الباصات
include('config.php');

try {
    // قراءة ملف SQL
    $sql_content = file_get_contents('bus_booking_system.sql');
    
    // تقسيم الاستعلامات
    $queries = explode(';', $sql_content);
    
    $success_count = 0;
    $error_count = 0;
    
    echo "<h2>تثبيت نظام حجز الباصات</h2>";
    echo "<div style='font-family: Arial; direction: rtl;'>";
    
    foreach ($queries as $query) {
        $query = trim($query);
        if (empty($query) || strpos($query, '--') === 0) {
            continue;
        }
        
        try {
            $stmt = $db->prepare($query);
            $stmt->execute();
            $success_count++;
            echo "<p style='color: green;'>✓ تم تنفيذ الاستعلام بنجاح</p>";
        } catch (PDOException $e) {
            $error_count++;
            echo "<p style='color: red;'>✗ خطأ في الاستعلام: " . $e->getMessage() . "</p>";
            echo "<pre style='background: #f5f5f5; padding: 10px; margin: 5px 0;'>" . htmlspecialchars($query) . "</pre>";
        }
    }
    
    echo "<hr>";
    echo "<h3>ملخص التثبيت:</h3>";
    echo "<p><strong>الاستعلامات الناجحة:</strong> $success_count</p>";
    echo "<p><strong>الاستعلامات الفاشلة:</strong> $error_count</p>";
    
    if ($error_count == 0) {
        echo "<p style='color: green; font-size: 18px;'><strong>✓ تم تثبيت نظام حجز الباصات بنجاح!</strong></p>";
        echo "<p><a href='bus-booking.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>انتقل إلى صفحة الحجز</a></p>";
    } else {
        echo "<p style='color: red; font-size: 18px;'><strong>✗ حدثت أخطاء أثناء التثبيت</strong></p>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ عام: " . $e->getMessage() . "</p>";
}
?>
