<?php
// وظائف نظام حجز الباصات

// دالة لإنشاء الرحلات اليومية
function generateDailyTrips($db, $days_ahead = 30) {
    try {
        // الحصول على جميع الطرق النشطة
        $stmt = $db->prepare("SELECT * FROM bus_routes WHERE status = 1 AND daily_schedule = 1");
        $stmt->execute();
        $routes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $trips_created = 0;
        
        foreach ($routes as $route) {
            // إنشاء رحلات للأيام القادمة
            for ($i = 0; $i <= $days_ahead; $i++) {
                $trip_date = date('Y-m-d', strtotime("+$i days"));
                
                // التحقق من وجود الرحلة
                $check_stmt = $db->prepare("SELECT id FROM bus_trips WHERE route_id = ? AND trip_date = ?");
                $check_stmt->execute([$route['id'], $trip_date]);
                
                if ($check_stmt->rowCount() == 0) {
                    // إنشاء الرحلة
                    $departure_datetime = $trip_date . ' ' . $route['departure_time'];
                    $arrival_datetime = $trip_date . ' ' . $route['arrival_time'];
                    
                    // إذا كان وقت الوصول في اليوم التالي
                    if ($route['arrival_time'] < $route['departure_time']) {
                        $arrival_datetime = date('Y-m-d H:i:s', strtotime($departure_datetime . ' +1 day'));
                        $arrival_datetime = date('Y-m-d', strtotime($arrival_datetime)) . ' ' . $route['arrival_time'];
                    }
                    
                    $insert_stmt = $db->prepare("
                        INSERT INTO bus_trips (route_id, trip_date, departure_datetime, arrival_datetime, available_seats) 
                        VALUES (?, ?, ?, ?, ?)
                    ");
                    $insert_stmt->execute([
                        $route['id'],
                        $trip_date,
                        $departure_datetime,
                        $arrival_datetime,
                        $route['bus_capacity']
                    ]);
                    
                    $trips_created++;
                }
            }
        }
        
        return $trips_created;
    } catch (Exception $e) {
        return false;
    }
}

// دالة للبحث عن الرحلات المتاحة
function searchTrips($db, $route_type, $origin_city_id, $trip_date, $seats_needed = 1) {
    try {
        $sql = "
            SELECT 
                t.id as trip_id,
                t.trip_date,
                t.departure_datetime,
                t.arrival_datetime,
                t.available_seats,
                t.booked_seats,
                r.route_name,
                r.price,
                r.route_type,
                os.name as origin_station,
                ds.name as destination_station,
                oc.name as origin_city,
                dc.name as destination_city
            FROM bus_trips t
            JOIN bus_routes r ON t.route_id = r.id
            JOIN bus_stations os ON r.origin_station_id = os.id
            JOIN bus_stations ds ON r.destination_station_id = ds.id
            JOIN bus_cities oc ON os.city_id = oc.id
            JOIN bus_cities dc ON ds.city_id = dc.id
            WHERE r.route_type = ? 
            AND os.city_id = ?
            AND t.trip_date = ?
            AND t.available_seats >= ?
            AND t.status = 'scheduled'
            AND r.status = 1
            ORDER BY t.departure_datetime
        ";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([$route_type, $origin_city_id, $trip_date, $seats_needed]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        return [];
    }
}

// دالة لإنشاء حجز جديد
function createBooking($db, $trip_id, $passenger_data, $seats_count) {
    try {
        $db->beginTransaction();
        
        // التحقق من توفر المقاعد
        $stmt = $db->prepare("SELECT available_seats, booked_seats FROM bus_trips WHERE id = ?");
        $stmt->execute([$trip_id]);
        $trip = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$trip || $trip['available_seats'] < $seats_count) {
            throw new Exception('المقاعد المطلوبة غير متوفرة');
        }
        
        // الحصول على سعر الرحلة
        $stmt = $db->prepare("
            SELECT r.price FROM bus_trips t 
            JOIN bus_routes r ON t.route_id = r.id 
            WHERE t.id = ?
        ");
        $stmt->execute([$trip_id]);
        $price = $stmt->fetchColumn();
        
        // إنشاء رقم حجز فريد
        $booking_reference = 'BUS' . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        // التحقق من عدم تكرار رقم الحجز
        $stmt = $db->prepare("SELECT id FROM bus_bookings WHERE booking_reference = ?");
        $stmt->execute([$booking_reference]);
        while ($stmt->rowCount() > 0) {
            $booking_reference = 'BUS' . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
            $stmt->execute([$booking_reference]);
        }
        
        $total_price = $price * $seats_count;
        
        // إدراج الحجز
        $stmt = $db->prepare("
            INSERT INTO bus_bookings 
            (booking_reference, trip_id, passenger_name, passenger_phone, passenger_email, 
             passenger_id_number, seats_count, total_price) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $booking_reference,
            $trip_id,
            $passenger_data['name'],
            $passenger_data['phone'],
            $passenger_data['email'] ?? null,
            $passenger_data['id_number'] ?? null,
            $seats_count,
            $total_price
        ]);
        
        $booking_id = $db->lastInsertId();
        
        // تحديث عدد المقاعد المتاحة
        $stmt = $db->prepare("
            UPDATE bus_trips 
            SET available_seats = available_seats - ?, 
                booked_seats = booked_seats + ? 
            WHERE id = ?
        ");
        $stmt->execute([$seats_count, $seats_count, $trip_id]);
        
        $db->commit();
        
        return [
            'success' => true,
            'booking_id' => $booking_id,
            'booking_reference' => $booking_reference,
            'total_price' => $total_price
        ];
        
    } catch (Exception $e) {
        $db->rollBack();
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

// دالة للحصول على تفاصيل الحجز
function getBookingDetails($db, $booking_reference) {
    try {
        $sql = "
            SELECT 
                b.*,
                t.trip_date,
                t.departure_datetime,
                t.arrival_datetime,
                r.route_name,
                os.name as origin_station,
                ds.name as destination_station,
                oc.name as origin_city,
                dc.name as destination_city
            FROM bus_bookings b
            JOIN bus_trips t ON b.trip_id = t.id
            JOIN bus_routes r ON t.route_id = r.id
            JOIN bus_stations os ON r.origin_station_id = os.id
            JOIN bus_stations ds ON r.destination_station_id = ds.id
            JOIN bus_cities oc ON os.city_id = oc.id
            JOIN bus_cities dc ON ds.city_id = dc.id
            WHERE b.booking_reference = ?
        ";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([$booking_reference]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        return false;
    }
}

// دالة للحصول على جميع المدن
function getAllCities($db) {
    try {
        $stmt = $db->prepare("SELECT * FROM bus_cities WHERE status = 1 ORDER BY name");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        return [];
    }
}

// دالة للحصول على محطات مدينة معينة
function getCityStations($db, $city_id) {
    try {
        $stmt = $db->prepare("SELECT * FROM bus_stations WHERE city_id = ? AND status = 1 ORDER BY name");
        $stmt->execute([$city_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        return [];
    }
}
?>
