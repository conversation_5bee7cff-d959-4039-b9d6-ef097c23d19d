<?php
// إعداد البيانات الأساسية لنظام حجز الباصات
include('config.php');
include('bus_functions.php');

echo "<h2>إعداد البيانات الأساسية لنظام حجز الباصات</h2>";
echo "<div style='font-family: Arial; direction: rtl;'>";

try {
    // إنشاء الرحلات اليومية للشهر القادم
    echo "<h3>إنشاء الرحلات اليومية...</h3>";
    $trips_created = generateDailyTrips($db, 30);
    
    if ($trips_created !== false) {
        echo "<p style='color: green;'>✓ تم إنشاء $trips_created رحلة يومية بنجاح</p>";
    } else {
        echo "<p style='color: red;'>✗ حدث خطأ في إنشاء الرحلات اليومية</p>";
    }
    
    // عرض إحصائيات النظام
    echo "<hr><h3>إحصائيات النظام:</h3>";
    
    // عدد المدن
    $stmt = $db->prepare("SELECT COUNT(*) FROM bus_cities WHERE status = 1");
    $stmt->execute();
    $cities_count = $stmt->fetchColumn();
    echo "<p><strong>عدد المدن:</strong> $cities_count</p>";
    
    // عدد المحطات
    $stmt = $db->prepare("SELECT COUNT(*) FROM bus_stations WHERE status = 1");
    $stmt->execute();
    $stations_count = $stmt->fetchColumn();
    echo "<p><strong>عدد المحطات:</strong> $stations_count</p>";
    
    // عدد الطرق
    $stmt = $db->prepare("SELECT COUNT(*) FROM bus_routes WHERE status = 1");
    $stmt->execute();
    $routes_count = $stmt->fetchColumn();
    echo "<p><strong>عدد الطرق:</strong> $routes_count</p>";
    
    // عدد الرحلات المجدولة
    $stmt = $db->prepare("SELECT COUNT(*) FROM bus_trips WHERE status = 'scheduled'");
    $stmt->execute();
    $scheduled_trips = $stmt->fetchColumn();
    echo "<p><strong>عدد الرحلات المجدولة:</strong> $scheduled_trips</p>";
    
    // عرض الطرق المتاحة
    echo "<hr><h3>الطرق المتاحة:</h3>";
    $stmt = $db->prepare("
        SELECT 
            r.route_name,
            r.route_type,
            r.departure_time,
            r.arrival_time,
            r.price,
            os.name as origin_station,
            ds.name as destination_station
        FROM bus_routes r
        JOIN bus_stations os ON r.origin_station_id = os.id
        JOIN bus_stations ds ON r.destination_station_id = ds.id
        WHERE r.status = 1
        ORDER BY r.route_type, r.departure_time
    ");
    $stmt->execute();
    $routes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='width: 100%; border-collapse: collapse;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>نوع الرحلة</th><th>من</th><th>إلى</th><th>وقت الانطلاق</th><th>وقت الوصول</th><th>السعر</th>";
    echo "</tr>";
    
    foreach ($routes as $route) {
        $route_type_ar = $route['route_type'] == 'to_mecca' ? 'ذهاب إلى مكة' : 'عودة من مكة';
        echo "<tr>";
        echo "<td>$route_type_ar</td>";
        echo "<td>{$route['origin_station']}</td>";
        echo "<td>{$route['destination_station']}</td>";
        echo "<td>" . date('g:i A', strtotime($route['departure_time'])) . "</td>";
        echo "<td>" . date('g:i A', strtotime($route['arrival_time'])) . "</td>";
        echo "<td>{$route['price']} ريال</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // عرض الرحلات لليوم الحالي
    echo "<hr><h3>رحلات اليوم (" . date('Y-m-d') . "):</h3>";
    $stmt = $db->prepare("
        SELECT 
            t.departure_datetime,
            t.available_seats,
            r.route_name,
            r.price,
            os.name as origin_station,
            ds.name as destination_station
        FROM bus_trips t
        JOIN bus_routes r ON t.route_id = r.id
        JOIN bus_stations os ON r.origin_station_id = os.id
        JOIN bus_stations ds ON r.destination_station_id = ds.id
        WHERE t.trip_date = CURDATE()
        AND t.status = 'scheduled'
        ORDER BY t.departure_datetime
    ");
    $stmt->execute();
    $today_trips = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($today_trips) > 0) {
        echo "<table border='1' style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>من</th><th>إلى</th><th>وقت الانطلاق</th><th>المقاعد المتاحة</th><th>السعر</th>";
        echo "</tr>";
        
        foreach ($today_trips as $trip) {
            echo "<tr>";
            echo "<td>{$trip['origin_station']}</td>";
            echo "<td>{$trip['destination_station']}</td>";
            echo "<td>" . date('g:i A', strtotime($trip['departure_datetime'])) . "</td>";
            echo "<td>{$trip['available_seats']}</td>";
            echo "<td>{$trip['price']} ريال</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>لا توجد رحلات مجدولة لليوم</p>";
    }
    
    // إنشاء رحلات تجريبية للأيام القادمة
    echo "<hr><h3>إنشاء رحلات تجريبية...</h3>";

    // حذف الرحلات الموجودة أولاً
    $db->exec("DELETE FROM bus_trips");

    // الحصول على جميع الطرق
    $stmt = $db->prepare("SELECT * FROM bus_routes WHERE status = 1");
    $stmt->execute();
    $routes = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $trips_created = 0;

    // إنشاء رحلات لمدة 30 يوم قادم
    for ($i = 0; $i < 30; $i++) {
        $trip_date = date('Y-m-d', strtotime("+$i days"));

        foreach ($routes as $route) {
            // إنشاء تاريخ ووقت الانطلاق
            $departure_datetime = $trip_date . ' ' . $route['departure_time'];
            $arrival_datetime = $trip_date . ' ' . $route['arrival_time'];

            try {
                $stmt = $db->prepare("
                    INSERT INTO bus_trips (
                        route_id, trip_date, departure_datetime, arrival_datetime,
                        available_seats, booked_seats, status
                    ) VALUES (?, ?, ?, ?, ?, 0, 'scheduled')
                ");
                $stmt->execute([
                    $route['id'],
                    $trip_date,
                    $departure_datetime,
                    $arrival_datetime,
                    $route['bus_capacity']
                ]);
                $trips_created++;
            } catch (PDOException $e) {
                // تجاهل الأخطاء المتعلقة بالتكرار
                if (strpos($e->getMessage(), 'Duplicate entry') === false) {
                    echo "<p style='color: orange;'>تحذير: " . $e->getMessage() . "</p>";
                }
            }
        }
    }

    echo "<p style='color: green;'>✓ تم إنشاء $trips_created رحلة تجريبية</p>";

    echo "<hr>";
    echo "<p style='color: green; font-size: 18px;'><strong>✓ تم إعداد النظام بنجاح!</strong></p>";
    echo "<p><a href='bus-booking.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>انتقل إلى صفحة الحجز</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
}

echo "</div>";
?>
