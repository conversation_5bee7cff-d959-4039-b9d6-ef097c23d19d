<?php
// ملف تشخيص بيانات نظام الباصات
include('config.php');

echo "<h2>تشخيص بيانات نظام الباصات</h2>";
echo "<div style='font-family: Arial; direction: rtl;'>";

try {
    // فحص المدن
    echo "<h3>المدن الموجودة:</h3>";
    $stmt = $db->prepare("SELECT * FROM bus_cities");
    $stmt->execute();
    $cities = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($cities) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>اسم المدينة</th><th>الحالة</th></tr>";
        foreach ($cities as $city) {
            echo "<tr><td>{$city['id']}</td><td>{$city['name']}</td><td>{$city['status']}</td></tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>لا توجد مدن!</p>";
    }
    
    // فحص المحطات
    echo "<h3>المحطات الموجودة:</h3>";
    $stmt = $db->prepare("
        SELECT s.*, c.name as city_name 
        FROM bus_stations s 
        JOIN bus_cities c ON s.city_id = c.id
    ");
    $stmt->execute();
    $stations = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($stations) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>اسم المحطة</th><th>المدينة</th><th>الحالة</th></tr>";
        foreach ($stations as $station) {
            echo "<tr><td>{$station['id']}</td><td>{$station['name']}</td><td>{$station['city_name']}</td><td>{$station['status']}</td></tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>لا توجد محطات!</p>";
    }
    
    // فحص الطرق
    echo "<h3>الطرق الموجودة:</h3>";
    $stmt = $db->prepare("
        SELECT r.*, 
               os.name as origin_station, 
               ds.name as destination_station,
               oc.name as origin_city,
               dc.name as destination_city
        FROM bus_routes r
        JOIN bus_stations os ON r.origin_station_id = os.id
        JOIN bus_stations ds ON r.destination_station_id = ds.id
        JOIN bus_cities oc ON os.city_id = oc.id
        JOIN bus_cities dc ON ds.city_id = dc.id
    ");
    $stmt->execute();
    $routes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($routes) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>اسم الطريق</th><th>النوع</th><th>من</th><th>إلى</th><th>وقت الانطلاق</th><th>السعر</th><th>الحالة</th></tr>";
        foreach ($routes as $route) {
            echo "<tr>";
            echo "<td>{$route['id']}</td>";
            echo "<td>{$route['route_name']}</td>";
            echo "<td>{$route['route_type']}</td>";
            echo "<td>{$route['origin_station']} ({$route['origin_city']})</td>";
            echo "<td>{$route['destination_station']} ({$route['destination_city']})</td>";
            echo "<td>{$route['departure_time']}</td>";
            echo "<td>{$route['price']} ريال</td>";
            echo "<td>{$route['status']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>لا توجد طرق!</p>";
    }
    
    // فحص الرحلات
    echo "<h3>الرحلات الموجودة:</h3>";
    $stmt = $db->prepare("SELECT COUNT(*) as total FROM bus_trips");
    $stmt->execute();
    $trips_count = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p>إجمالي الرحلات: <strong>{$trips_count['total']}</strong></p>";
    
    if ($trips_count['total'] > 0) {
        $stmt = $db->prepare("
            SELECT t.*, r.route_name, 
                   os.name as origin_station, 
                   ds.name as destination_station
            FROM bus_trips t
            JOIN bus_routes r ON t.route_id = r.id
            JOIN bus_stations os ON r.origin_station_id = os.id
            JOIN bus_stations ds ON r.destination_station_id = ds.id
            ORDER BY t.trip_date, t.departure_datetime
            LIMIT 10
        ");
        $stmt->execute();
        $trips = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>الطريق</th><th>من</th><th>إلى</th><th>تاريخ الرحلة</th><th>وقت الانطلاق</th><th>المقاعد المتاحة</th></tr>";
        foreach ($trips as $trip) {
            echo "<tr>";
            echo "<td>{$trip['id']}</td>";
            echo "<td>{$trip['route_name']}</td>";
            echo "<td>{$trip['origin_station']}</td>";
            echo "<td>{$trip['destination_station']}</td>";
            echo "<td>{$trip['trip_date']}</td>";
            echo "<td>" . date('g:i A', strtotime($trip['departure_datetime'])) . "</td>";
            echo "<td>{$trip['available_seats']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p><em>عرض أول 10 رحلات فقط</em></p>";
    }
    
    // محاولة إنشاء رحلة تجريبية يدوياً
    echo "<hr><h3>محاولة إنشاء رحلة تجريبية:</h3>";
    
    if (count($routes) > 0) {
        $test_route = $routes[0];
        $test_date = date('Y-m-d', strtotime('+1 day'));
        $departure_datetime = $test_date . ' ' . $test_route['departure_time'];
        $arrival_datetime = $test_date . ' ' . $test_route['arrival_time'];
        
        try {
            $stmt = $db->prepare("
                INSERT INTO bus_trips (
                    route_id, trip_date, departure_datetime, arrival_datetime, 
                    available_seats, booked_seats, status
                ) VALUES (?, ?, ?, ?, ?, 0, 'scheduled')
            ");
            $result = $stmt->execute([
                $test_route['id'],
                $test_date,
                $departure_datetime,
                $arrival_datetime,
                $test_route['bus_capacity']
            ]);
            
            if ($result) {
                echo "<p style='color: green;'>✓ تم إنشاء رحلة تجريبية بنجاح!</p>";
                echo "<p>الطريق: {$test_route['route_name']}</p>";
                echo "<p>التاريخ: $test_date</p>";
                echo "<p>وقت الانطلاق: $departure_datetime</p>";
            } else {
                echo "<p style='color: red;'>✗ فشل في إنشاء الرحلة التجريبية</p>";
            }
        } catch (PDOException $e) {
            echo "<p style='color: red;'>خطأ في إنشاء الرحلة: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: red;'>لا يمكن إنشاء رحلة تجريبية - لا توجد طرق!</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
}

echo "</div>";
?>
