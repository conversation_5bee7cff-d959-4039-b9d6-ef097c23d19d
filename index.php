<?php
ob_start();
header('Content-Type: text/html; charset=utf-8');
date_default_timezone_set("Africa/Cairo");
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);


include('webset.php');
include('session.php'); 

$actual_link = explode('?' , $actual_link)[0];

$REQUEST_URI = $_SERVER['REQUEST_URI']; 
$REQUEST_URI = str_replace($Site_URL, '' , $actual_link); 
$REQUEST_URI = rtrim($REQUEST_URI, '/');
$MasterDomain = parse_url("https://$_SERVER[HTTP_HOST]")['host'];

$FURL = explode('/',$REQUEST_URI);
$MyCase = count($FURL) > 1 ? $FURL[1] : $FURL[0];
$Hamalt = [];

switch ($MyCase) {
    case '':
    case '/':
        include('home.php'); break;

	case 'terms-and-conditions':
		include('terms-and-conditions.php'); break;
		
	case 'about':
		include('about.php'); break;

	case 'privacy-policy':
		include('privacy-policy.php'); break;
  
	case 'contact-us':
		include('contact-us.php'); break;	

	case 'cities':
		include('cities-all.php'); break;

	case 'hotel':
		if(isset($FURL[2]) && $FURL[2] != ''){
			$HotelData = getAllFrom('*' , 'hotels_booking' , 'WHERE status=1 AND ( link = "'.$FURL[2].'" OR link = "'.urldecode($FURL[2]).'" ) ', 'ORDER BY id DESC');
			if(count($HotelData) > 0){
				include('hotel-details.php'); break;
			}else{
				http_response_code(404);
				header("Location:".$Site_URL."/error404"); exit();
			}
		}else{
			include('hotel.php'); break;
		}

	case 'hotel-booking':
		if(isset($FURL[2]) && $FURL[2] != ''){
			include('hotel-booking.php'); break;
		}else{
			http_response_code(404);
			header("Location:".$Site_URL."/error404"); exit();
		}



	case 'login':
		include('login.php'); break;

	case 'signup':
		include('signup.php'); break;

	case 'logout':
		include('logout.php'); break;

	case 'invoice':
		include('invoice.php'); break;

	case 'confirm':
		if(isset($FURL[2]) && $FURL[2] != ''){
			$dxxxx = json_decode(base64_decode($FURL[2]));
			$UmrahData = getAllFrom('*' , 'offers' , 'WHERE id = "'.$dxxxx->id.'" ', '');
			if(count($UmrahData) > 0){
				include('confirm.php'); break;
			}else{
				http_response_code(404);
				header("Location:".$Site_URL."/error404"); exit();
			}
		}else{
			http_response_code(404);
			header("Location:".$Site_URL."/error404"); exit();
		}
	
	case 'error404':
		include('error404.php'); break;

	case 'bus-booking':
		include('bus-booking.php'); break;

	case 'bus-booking-form':
		include('bus-booking-form.php'); break;

	case 'bus-booking-search':
		include('bus-booking-search.php'); break;

	case 'bus-ticket':
		include('bus-ticket.php'); break;

    default:
		 
		$CitiesData = getAllFrom('*' , 'cities' , 'WHERE status=1 AND ( link = "'.$MyCase.'" OR link = "'.urldecode($MyCase).'" ) ', 'ORDER BY orders DESC , id DESC');

		if(count($CitiesData) > 0){

			$c1 = getAllFrom('id' , 'offers' , 'WHERE status=1', '');
			$c2 = getAllFrom('id' , 'hotels' , '', '');

			$Hamalt =   count($c1) > 0 && count($c2) > 0 ?
			 getAllFrom('*,offers.price as offer_price,offers.status as offer_status,offers.id as offer_id' , 'offers' , 'INNER JOIN hotels ON JSON_UNQUOTE(JSON_EXTRACT(offers.hotelid, "$.k")) = hotels.id WHERE offers.catid = "'.$CitiesData[0]['id'].'"  AND offers.status = 1 ', 'ORDER BY offers.status DESC , offers.orders ASC , offers.id DESC') : [];
			include('cities.php');
			exit;
		}else{
			// البحث في أقسام الفنادق
			$HotelCityData = getAllFrom('*', 'hotel_city', 'WHERE status=1 AND ( slug = "'.$MyCase.'" OR slug = "'.urldecode($MyCase).'" )', 'ORDER BY orders ASC, id ASC');

			if(count($HotelCityData) > 0){
				// جلب فنادق المدينة المحددة من جدول الحجز المباشر
				$AllHotel = getAllFrom('*', 'hotels_booking h JOIN hotel_city hc ON h.hotel_city_id = hc.id', 'WHERE h.status = 1 AND hc.slug = "'.$MyCase.'"', 'ORDER BY h.star_rating DESC, h.id DESC');
				include('hotel.php');
				exit;
			}
		}

		if(strpos($MyCase, $UmrahUrl) !== false){
			$uid = trim(str_replace($UmrahUrl , '' , $MyCase));
			$UmrahData = getAllFrom('*' , 'offers' , 'WHERE id = "'.$uid.'" ', '');
			if(count($UmrahData) > 0){
				include('umrah-details.php'); break;
			}
		}else{ 
			http_response_code(404);
			header("Location:".$Site_URL."/error404"); exit();
		}
		 
}

exit;

 