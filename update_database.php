<?php
// سكريبت تحديث قاعدة البيانات لإضافة جداول نظام حجز الباصات
include('config.php');

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='utf-8'>
    <title>تحديث قاعدة البيانات - نظام حجز الباصات</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .step { margin: 15px 0; padding: 10px; border-right: 4px solid #007bff; background: #f8f9fa; }
    </style>
</head>
<body>
<div class='container'>
    <h1>تحديث قاعدة البيانات - نظام حجز الباصات</h1>";

try {
    // التحقق من الاتصال بقاعدة البيانات
    echo "<div class='step'><h3>الخطوة 1: التحقق من الاتصال بقاعدة البيانات</h3>";
    if ($db) {
        echo "<div class='success'>✓ تم الاتصال بقاعدة البيانات بنجاح</div>";
    } else {
        throw new Exception("فشل في الاتصال بقاعدة البيانات");
    }
    echo "</div>";

    // إنشاء جدول محطات الباصات
    echo "<div class='step'><h3>الخطوة 2: إنشاء جدول محطات الباصات</h3>";
    $sql_stations = "CREATE TABLE IF NOT EXISTS `bus_stations` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(255) NOT NULL,
        `city_id` int(11) NOT NULL,
        `address` text DEFAULT NULL,
        `status` tinyint(1) NOT NULL DEFAULT 1,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `city_id` (`city_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    if ($db->exec($sql_stations) !== false) {
        echo "<div class='success'>✓ تم إنشاء جدول محطات الباصات بنجاح</div>";
    } else {
        echo "<div class='error'>✗ خطأ في إنشاء جدول محطات الباصات</div>";
    }
    echo "</div>";

    // إنشاء جدول رحلات الباصات
    echo "<div class='step'><h3>الخطوة 3: إنشاء جدول رحلات الباصات</h3>";
    $sql_trips = "CREATE TABLE IF NOT EXISTS `bus_trips` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `trip_type` enum('to_mecca','from_mecca') NOT NULL COMMENT 'نوع الرحلة: ذهاب إلى مكة أو عودة من مكة',
        `departure_station_id` int(11) NOT NULL COMMENT 'محطة المغادرة',
        `arrival_station_id` int(11) NOT NULL COMMENT 'محطة الوصول',
        `departure_time` time NOT NULL COMMENT 'وقت المغادرة',
        `arrival_time` time NOT NULL COMMENT 'وقت الوصول المتوقع',
        `price_per_seat` decimal(10,2) NOT NULL COMMENT 'سعر المقعد الواحد',
        `total_seats` int(11) NOT NULL DEFAULT 50 COMMENT 'إجمالي المقاعد',
        `available_seats` int(11) NOT NULL DEFAULT 50 COMMENT 'المقاعد المتاحة',
        `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1=نشط, 0=غير نشط',
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `departure_station_id` (`departure_station_id`),
        KEY `arrival_station_id` (`arrival_station_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    if ($db->exec($sql_trips) !== false) {
        echo "<div class='success'>✓ تم إنشاء جدول رحلات الباصات بنجاح</div>";
    } else {
        echo "<div class='error'>✗ خطأ في إنشاء جدول رحلات الباصات</div>";
    }
    echo "</div>";

    // إنشاء جدول حجوزات الباصات
    echo "<div class='step'><h3>الخطوة 4: إنشاء جدول حجوزات الباصات</h3>";
    $sql_bookings = "CREATE TABLE IF NOT EXISTS `bus_bookings` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `trip_id` int(11) NOT NULL,
        `trip_date` date NOT NULL COMMENT 'تاريخ الرحلة',
        `passenger_name` varchar(255) NOT NULL,
        `passenger_phone` varchar(20) NOT NULL,
        `passenger_email` varchar(255) DEFAULT NULL,
        `passenger_id_number` varchar(20) DEFAULT NULL,
        `seats_count` int(11) NOT NULL DEFAULT 1,
        `total_price` decimal(10,2) NOT NULL,
        `booking_status` enum('pending','confirmed','cancelled','completed') NOT NULL DEFAULT 'pending',
        `payment_status` enum('pending','paid','refunded') NOT NULL DEFAULT 'pending',
        `booking_reference` varchar(50) DEFAULT NULL,
        `notes` text DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `booking_reference` (`booking_reference`),
        KEY `trip_id` (`trip_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    if ($db->exec($sql_bookings) !== false) {
        echo "<div class='success'>✓ تم إنشاء جدول حجوزات الباصات بنجاح</div>";
    } else {
        echo "<div class='error'>✗ خطأ في إنشاء جدول حجوزات الباصات</div>";
    }
    echo "</div>";

    // التحقق من وجود المدن وإضافة مكة إذا لم تكن موجودة
    echo "<div class='step'><h3>الخطوة 5: التحقق من المدن وإضافة مكة المكرمة</h3>";
    
    // البحث عن مكة المكرمة
    $mecca_check = $db->query("SELECT id FROM cities WHERE name LIKE '%مكة%' OR name LIKE '%مكة المكرمة%'")->fetch();
    
    if (!$mecca_check) {
        // إضافة مكة المكرمة
        $insert_mecca = $db->prepare("INSERT INTO cities (name, status, dateCreated) VALUES (?, 1, ?)");
        $insert_mecca->execute(['مكة المكرمة', date('Y-m-d H:i:s')]);
        $mecca_id = $db->lastInsertId();
        echo "<div class='success'>✓ تم إضافة مكة المكرمة إلى قائمة المدن (ID: $mecca_id)</div>";
    } else {
        $mecca_id = $mecca_check['id'];
        echo "<div class='info'>ℹ مكة المكرمة موجودة بالفعل (ID: $mecca_id)</div>";
    }
    echo "</div>";

    // إضافة المحطات
    echo "<div class='step'><h3>الخطوة 6: إضافة محطات الباصات</h3>";

    // حذف المحطات الموجودة وإعادة إنشائها
    $db->exec("DELETE FROM bus_bookings");
    $db->exec("DELETE FROM bus_trips");
    $db->exec("DELETE FROM bus_stations");
    echo "<div class='info'>ℹ تم حذف جميع المحطات والرحلات الموجودة</div>";

    // البحث عن الرياض في المدن
    $riyadh_check = $db->query("SELECT id FROM cities WHERE name LIKE '%رياض%' OR name LIKE '%الرياض%'")->fetch();

    if (!$riyadh_check) {
        // إضافة الرياض
        $insert_riyadh = $db->prepare("INSERT INTO cities (name, status, dateCreated) VALUES (?, 1, ?)");
        $insert_riyadh->execute(['الرياض', date('Y-m-d H:i:s')]);
        $riyadh_id = $db->lastInsertId();
        echo "<div class='success'>✓ تم إضافة الرياض إلى قائمة المدن (ID: $riyadh_id)</div>";
    } else {
        $riyadh_id = $riyadh_check['id'];
        echo "<div class='info'>ℹ الرياض موجودة بالفعل (ID: $riyadh_id)</div>";
    }

    // إضافة المحطات (الرياض ومكة فقط)
    $stations_data = [
        ['محطة الرياض - البطحاء', $riyadh_id, 'حي البطحاء، الرياض'],
        ['محطة مكة المركزية', $mecca_id, 'شارع إبراهيم الخليل، مكة المكرمة']
    ];

    $insert_station = $db->prepare("INSERT INTO bus_stations (name, city_id, address) VALUES (?, ?, ?)");

    foreach ($stations_data as $station) {
        $insert_station->execute($station);
    }

    echo "<div class='success'>✓ تم إضافة " . count($stations_data) . " محطة باص (الرياض - البطحاء ومكة المركزية)</div>";
    echo "</div>";

    // إضافة الرحلات التجريبية
    echo "<div class='step'><h3>الخطوة 7: إضافة رحلات الرياض - مكة</h3>";

    // الحصول على معرفات المحطات
    $riyadh_station = $db->query("SELECT id FROM bus_stations WHERE name LIKE '%البطحاء%'")->fetch()['id'];
    $mecca_station = $db->query("SELECT id FROM bus_stations WHERE name LIKE '%مكة المركزية%'")->fetch()['id'];

    if ($riyadh_station && $mecca_station) {
        $trips_data = [
            // رحلات الذهاب من الرياض إلى مكة
            ['to_mecca', $riyadh_station, $mecca_station, '06:00:00', '16:00:00', 150.00],
            ['to_mecca', $riyadh_station, $mecca_station, '14:00:00', '00:00:00', 150.00],
            ['to_mecca', $riyadh_station, $mecca_station, '22:00:00', '08:00:00', 140.00],
            // رحلات العودة من مكة إلى الرياض
            ['from_mecca', $mecca_station, $riyadh_station, '08:00:00', '18:00:00', 150.00],
            ['from_mecca', $mecca_station, $riyadh_station, '16:00:00', '02:00:00', 150.00],
            ['from_mecca', $mecca_station, $riyadh_station, '23:00:00', '09:00:00', 140.00]
        ];

        $insert_trip = $db->prepare("INSERT INTO bus_trips (trip_type, departure_station_id, arrival_station_id, departure_time, arrival_time, price_per_seat) VALUES (?, ?, ?, ?, ?, ?)");

        foreach ($trips_data as $trip) {
            $insert_trip->execute($trip);
        }

        echo "<div class='success'>✓ تم إضافة " . count($trips_data) . " رحلة بين الرياض (البطحاء) ومكة المكرمة</div>";
        echo "<div class='info'>ℹ محطة الرياض: ID $riyadh_station | محطة مكة: ID $mecca_station</div>";
    } else {
        echo "<div class='error'>✗ لم يتم العثور على المحطات المطلوبة</div>";
    }
    echo "</div>";

    echo "<div class='step'><h3>✅ تم الانتهاء من تحديث قاعدة البيانات بنجاح!</h3>";
    echo "<div class='success'>
        <p><strong>تم إنشاء الجداول التالية:</strong></p>
        <ul>
            <li>bus_stations - محطات الباصات</li>
            <li>bus_trips - رحلات الباصات</li>
            <li>bus_bookings - حجوزات الباصات</li>
        </ul>
        <p><strong>يمكنك الآن:</strong></p>
        <ul>
            <li>زيارة صفحة حجز الباصات: <a href='bus-booking' target='_blank'>bus-booking</a></li>
            <li>حذف هذا الملف لأسباب أمنية</li>
        </ul>
    </div>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='error'><strong>خطأ:</strong> " . $e->getMessage() . "</div>";
}

echo "
</div>
</body>
</html>";
?>
